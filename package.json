{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "chokidar": "^4.0.3", "concurrently": "^9.1.2", "laravel-echo": "^2.1.5", "laravel-vite-plugin": "^1.3.0", "postcss": "^8.5.5", "pusher-js": "^8.4.0", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}, "dependencies": {"@puppeteer/browsers": "^2.10.5", "postcss-nesting": "^13.0.2", "puppeteer": "^24.10.1", "puppeteer-core": "^24.10.1"}}