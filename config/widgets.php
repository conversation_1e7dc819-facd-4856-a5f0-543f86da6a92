<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Widget Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for Filament widgets used
    | throughout the DCCP Admin system.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    | Configure caching behavior for widgets to improve performance.
    |
    */
    'cache_duration' => env('WIDGET_CACHE_DURATION', 900), // 15 minutes default

    /*
    |--------------------------------------------------------------------------
    | Chart Widget Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for chart widgets including colors and display options.
    |
    */
    'chart' => [
        'default_colors' => [
            '#3B82F6', // Blue
            '#10B981', // Green
            '#F59E0B', // Amber
            '#EF4444', // Red
            '#8B5CF6', // Purple
            '#F97316', // Orange
            '#06B6D4', // Cyan
            '#84CC16', // Lime
            '#EC4899', // Pink
            '#6B7280', // Gray
        ],
        'max_items' => 20, // Maximum items to display in charts
        'polling_interval' => '30s',
    ],

    /*
    |--------------------------------------------------------------------------
    | Course Distribution Widget
    |--------------------------------------------------------------------------
    |
    | Specific settings for the Course Distribution Widget.
    |
    */
    'course_distribution' => [
        'cache_duration' => env('COURSE_DISTRIBUTION_CACHE_DURATION', 900),
        'max_courses' => 20,
        'default_status_filter' => 'Active',
        'enable_school_year_filter' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings to optimize widget performance.
    |
    */
    'performance' => [
        'enable_query_logging' => env('WIDGET_QUERY_LOGGING', false),
        'slow_query_threshold' => 1000, // milliseconds
    ],
];
