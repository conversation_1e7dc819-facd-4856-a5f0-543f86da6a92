<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Chrome/Chromium Binary Path
    |--------------------------------------------------------------------------
    |
    | The path to the Chrome or Chromium binary. If not set, Browsershot will
    | try to find the binary automatically. For Nix environments, this should
    | be set via the CHROME_PATH environment variable.
    |
    */
    'chrome_path' => env('CHROME_PATH', '/usr/bin/google-chrome-stable'),

    /*
    |--------------------------------------------------------------------------
    | Node Binary Path
    |--------------------------------------------------------------------------
    |
    | The path to the Node.js binary. If not set, Browsershot will use the
    | default Node.js installation. For Nix environments, this should be
    | set via the NODE_BINARY_PATH environment variable.
    |
    */
    'node_binary_path' => env(
        'NODE_BINARY_PATH',
        '/root/.nix-profile/bin/node'
    ),

    /*
    |--------------------------------------------------------------------------
    | NPM Binary Path
    |--------------------------------------------------------------------------
    |
    | The path to the NPM binary. If not set, Browsershot will use the
    | default NPM installation. For Nix environments, this should be
    | set via the NPM_BINARY_PATH environment variable.
    |
    */
    'npm_binary_path' => env('NPM_BINARY_PATH', '/tmp/fake-npm-bin/npm.js'),

    /*
    |--------------------------------------------------------------------------
    | Default Options
    |--------------------------------------------------------------------------
    |
    | Default options to apply to all Browsershot instances. These can be
    | overridden on a per-instance basis.
    |
    */
    'default_options' => [
        'no_sandbox' => env('BROWSERSHOT_NO_SANDBOX', true),
        'disable_web_security' => env('BROWSERSHOT_DISABLE_WEB_SECURITY', true),
        'ignore_https_errors' => env('BROWSERSHOT_IGNORE_HTTPS_ERRORS', true),
        'timeout' => env('BROWSERSHOT_TIMEOUT', 120),
        'chrome_args' => [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--no-zygote',
            '--single-process',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-extensions',
            '--disable-sync',
            '--disable-background-networking',
            '--disable-translate',
            '--disable-notifications',
            '--disable-geolocation',
            '--no-default-browser-check',
            '--user-data-dir=/tmp/chrome-user-data',
            '--crash-dumps-dir=/tmp/chrome-crashpad',
            '--disable-crash-reporter',
            '--disable-breakpad',
            '--mute-audio',
            '--hide-scrollbars',
            '--remote-debugging-port=0',
            '--disable-software-rasterizer',
            '--virtual-time-budget=30000',
            '--timeout=120000',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Temporary Directory
    |--------------------------------------------------------------------------
    |
    | The directory where Browsershot will store temporary files. If not set,
    | the system temporary directory will be used.
    |
    */
    'temp_directory' => env(
        'BROWSERSHOT_TEMP_DIRECTORY',
        storage_path('app/browsershot-temp')
    ),

    /*
    |--------------------------------------------------------------------------
    | PDF Options
    |--------------------------------------------------------------------------
    |
    | Default options for PDF generation.
    |
    */
    'pdf_options' => [
        'format' => env('BROWSERSHOT_PDF_FORMAT', 'A4'),
        'margin_top' => env('BROWSERSHOT_PDF_MARGIN_TOP', 0),
        'margin_bottom' => env('BROWSERSHOT_PDF_MARGIN_BOTTOM', 0),
        'margin_left' => env('BROWSERSHOT_PDF_MARGIN_LEFT', 0),
        'margin_right' => env('BROWSERSHOT_PDF_MARGIN_RIGHT', 0),
        'print_background' => env('BROWSERSHOT_PDF_PRINT_BACKGROUND', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Screenshot Options
    |--------------------------------------------------------------------------
    |
    | Default options for screenshot generation.
    |
    */
    'screenshot_options' => [
        'width' => env('BROWSERSHOT_SCREENSHOT_WIDTH', 1920),
        'height' => env('BROWSERSHOT_SCREENSHOT_HEIGHT', 1080),
        'device_scale_factor' => env('BROWSERSHOT_DEVICE_SCALE_FACTOR', 1),
        'full_page' => env('BROWSERSHOT_FULL_PAGE', false),
    ],
];
