<?php

namespace Tests\Feature;

use App\Models\Classes;
use App\Models\Faculty;
use App\Models\Room;
use App\Models\Schedule;
use App\Models\Subject;
use App\Services\GeneralSettingsService;
use App\Services\TimetableConflictService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TimetableConflictTest extends TestCase
{
    use RefreshDatabase;

    protected TimetableConflictService $conflictService;

    protected GeneralSettingsService $settingsService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->settingsService = $this->app->make(GeneralSettingsService::class);
        $this->conflictService = new TimetableConflictService($this->settingsService);
    }

    /** @test */
    public function it_detects_room_time_conflicts()
    {
        // Create test data
        $room = Room::factory()->create();
        $faculty = Faculty::factory()->create();
        $subject1 = Subject::factory()->create();
        $subject2 = Subject::factory()->create();

        $class1 = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject1->code,
        ]);

        $class2 = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject2->code,
        ]);

        // Create conflicting schedules (same room, same time)
        $schedule1 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class1->id,
            'day_of_week' => 'Monday',
            'start_time' => '09:00:00',
            'end_time' => '10:00:00',
        ]);

        $schedule2 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class2->id,
            'day_of_week' => 'Monday',
            'start_time' => '09:30:00',
            'end_time' => '10:30:00',
        ]);

        $schedules = collect([$schedule1, $schedule2]);
        $conflicts = $this->conflictService->detectConflicts($schedules);

        $this->assertNotEmpty($conflicts['time_room_conflicts']);
        $this->assertCount(1, $conflicts['time_room_conflicts']);

        $conflict = $conflicts['time_room_conflicts'][0];
        $this->assertEquals('high', $conflict['severity']);
        $this->assertEquals('time_room', $conflict['type']);
    }

    /** @test */
    public function it_detects_faculty_conflicts()
    {
        // Create test data
        $room1 = Room::factory()->create();
        $room2 = Room::factory()->create();
        $faculty = Faculty::factory()->create();
        $subject1 = Subject::factory()->create();
        $subject2 = Subject::factory()->create();

        $class1 = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject1->code,
        ]);

        $class2 = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject2->code,
        ]);

        // Create conflicting schedules (same faculty, different rooms, overlapping time)
        $schedule1 = Schedule::factory()->create([
            'room_id' => $room1->id,
            'class_id' => $class1->id,
            'day_of_week' => 'Tuesday',
            'start_time' => '14:00:00',
            'end_time' => '15:00:00',
        ]);

        $schedule2 = Schedule::factory()->create([
            'room_id' => $room2->id,
            'class_id' => $class2->id,
            'day_of_week' => 'Tuesday',
            'start_time' => '14:30:00',
            'end_time' => '15:30:00',
        ]);

        $schedules = collect([$schedule1, $schedule2]);
        $conflicts = $this->conflictService->detectConflicts($schedules);

        $this->assertNotEmpty($conflicts['faculty_conflicts']);
        $this->assertCount(1, $conflicts['faculty_conflicts']);

        $conflict = $conflicts['faculty_conflicts'][0];
        $this->assertEquals('high', $conflict['severity']);
        $this->assertEquals('faculty', $conflict['type']);
    }

    /** @test */
    public function it_does_not_detect_conflicts_for_non_overlapping_schedules()
    {
        // Create test data
        $room = Room::factory()->create();
        $faculty = Faculty::factory()->create();
        $subject1 = Subject::factory()->create();
        $subject2 = Subject::factory()->create();

        $class1 = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject1->code,
        ]);

        $class2 = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject2->code,
        ]);

        // Create non-overlapping schedules
        $schedule1 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class1->id,
            'day_of_week' => 'Wednesday',
            'start_time' => '09:00:00',
            'end_time' => '10:00:00',
        ]);

        $schedule2 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class2->id,
            'day_of_week' => 'Wednesday',
            'start_time' => '10:00:00',
            'end_time' => '11:00:00',
        ]);

        $schedules = collect([$schedule1, $schedule2]);
        $conflicts = $this->conflictService->detectConflicts($schedules);

        $this->assertEmpty($conflicts['time_room_conflicts']);
        $this->assertEmpty($conflicts['faculty_conflicts']);
    }

    /** @test */
    public function it_generates_correct_conflict_summary()
    {
        // Create test data with multiple conflicts
        $room = Room::factory()->create();
        $faculty1 = Faculty::factory()->create();
        $faculty2 = Faculty::factory()->create();
        $subject1 = Subject::factory()->create();
        $subject2 = Subject::factory()->create();
        $subject3 = Subject::factory()->create();

        $class1 = Classes::factory()->create([
            'faculty_id' => $faculty1->id,
            'subject_code' => $subject1->code,
        ]);

        $class2 = Classes::factory()->create([
            'faculty_id' => $faculty1->id,
            'subject_code' => $subject2->code,
        ]);

        $class3 = Classes::factory()->create([
            'faculty_id' => $faculty2->id,
            'subject_code' => $subject3->code,
        ]);

        // Create multiple conflicting schedules
        $schedule1 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class1->id,
            'day_of_week' => 'Thursday',
            'start_time' => '11:00:00',
            'end_time' => '12:00:00',
        ]);

        $schedule2 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class2->id,
            'day_of_week' => 'Thursday',
            'start_time' => '11:30:00',
            'end_time' => '12:30:00',
        ]);

        $schedule3 = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class3->id,
            'day_of_week' => 'Thursday',
            'start_time' => '11:45:00',
            'end_time' => '12:45:00',
        ]);

        $schedules = collect([$schedule1, $schedule2, $schedule3]);
        $conflicts = $this->conflictService->detectConflicts($schedules);
        $summary = $this->conflictService->getConflictSummary($conflicts);

        $this->assertGreaterThan(0, $summary['total_conflicts']);
        $this->assertGreaterThan(0, $summary['high_severity']);
        $this->assertArrayHasKey('by_type', $summary);
        $this->assertArrayHasKey('time_room', $summary['by_type']);
    }

    /** @test */
    public function it_provides_correct_color_coding()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        // Test college classification
        $collegeClass = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'college',
        ]);

        $collegeSchedule = Schedule::factory()->create([
            'class_id' => $collegeClass->id,
        ]);

        $collegeColors = $this->conflictService->getScheduleColorCode($collegeSchedule);
        $this->assertStringContains('blue', $collegeColors['bg']);

        // Test SHS classification
        $shsClass = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'shs',
        ]);

        $shsSchedule = Schedule::factory()->create([
            'class_id' => $shsClass->id,
        ]);

        $shsColors = $this->conflictService->getScheduleColorCode($shsSchedule);
        $this->assertStringContains('green', $shsColors['bg']);
    }

    /** @test */
    public function it_caches_conflict_detection_results()
    {
        $room = Room::factory()->create();
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
        ]);

        $schedule = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class->id,
        ]);

        $schedules = collect([$schedule]);
        $cacheKey = 'test_conflict_cache';

        // First call should cache the result
        $conflicts1 = $this->conflictService->getCachedConflicts($cacheKey, $schedules);

        // Second call should return cached result
        $conflicts2 = $this->conflictService->getCachedConflicts($cacheKey, $schedules);

        $this->assertEquals($conflicts1, $conflicts2);

        // Clear cache and verify it's cleared
        $this->conflictService->clearConflictCache($cacheKey);

        // This should work without errors
        $this->assertTrue(true);
    }

    /** @test */
    public function it_handles_empty_schedule_collections()
    {
        $emptySchedules = collect([]);
        $conflicts = $this->conflictService->detectConflicts($emptySchedules);

        $this->assertIsArray($conflicts);
        $this->assertEmpty($conflicts['time_room_conflicts']);
        $this->assertEmpty($conflicts['faculty_conflicts']);
        $this->assertEmpty($conflicts['student_conflicts']);
    }

    /** @test */
    public function it_calculates_overlap_details_correctly()
    {
        $schedule1 = [
            'start_time' => '09:00:00',
            'end_time' => '10:30:00',
        ];

        $schedule2 = [
            'start_time' => '09:45:00',
            'end_time' => '11:00:00',
        ];

        $reflection = new \ReflectionClass($this->conflictService);
        $method = $reflection->getMethod('getOverlapDetails');
        $method->setAccessible(true);

        $overlapDetails = $method->invoke($this->conflictService, $schedule1, $schedule2);

        $this->assertEquals('09:45', $overlapDetails['overlap_start']);
        $this->assertEquals('10:30', $overlapDetails['overlap_end']);
        $this->assertEquals(45, $overlapDetails['overlap_duration']);
    }

    /** @test */
    public function it_integrates_with_timetable_page_correctly()
    {
        // This test would verify the integration with the Timetable page class
        // For now, we'll just verify the service can be instantiated
        $this->assertInstanceOf(TimetableConflictService::class, $this->conflictService);
    }
}
