<?php

namespace Tests\Feature;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

beforeEach(function (): void {
    // Skip if Supabase is not configured
    if (empty(config('filesystems.disks.supabase.key'))) {
        $this->markTestSkipped('Supabase storage is not configured.');
    }

    // Create a test file
    $this->testContent = 'Testing Supabase storage at '.now()->toDateTimeString();
    $this->testPath = 'test/test-'.Str::random(10).'.txt';
});

afterEach(function (): void {
    // Cleanup test files
    if (isset($this->testPath)) {
        Storage::disk('supabase')->delete($this->testPath);
    }
});

test('supabase environment variables are properly configured', function (): void {
    $requiredConfigs = [
        'SUPABASE_STORAGE_KEY' => env('SUPABASE_STORAGE_KEY'),
        'SUPABASE_STORAGE_BUCKET' => env('SUPABASE_STORAGE_BUCKET'),
        'SUPABASE_STORAGE_ENDPOINT' => env('SUPABASE_STORAGE_ENDPOINT'),
    ];

    foreach ($requiredConfigs as $key => $value) {
        expect($value)
            ->not->toBeNull("$key is not set in .env")
            ->not->toBeEmpty("$key is empty in .env");
    }

    $supabaseConfig = Config::get('filesystems.disks.supabase');

    expect($supabaseConfig)
        ->toBeArray()
        ->toHaveKeys(['driver', 'key', 'bucket', 'endpoint'])
        ->and($supabaseConfig['driver'])->toBe('supabase')
        ->and($supabaseConfig['key'])->toBe(env('SUPABASE_STORAGE_KEY'))
        ->and($supabaseConfig['bucket'])->toBe(env('SUPABASE_STORAGE_BUCKET'))
        ->and($supabaseConfig['endpoint'])->toBe(env('SUPABASE_STORAGE_ENDPOINT'));
});

test('can write and read file on supabase', function (): void {
    $disk = Storage::disk('supabase');

    // Test file upload using put()
    $uploaded = $disk->put($this->testPath, $this->testContent);

    expect($uploaded)->toBeTrue('Failed to upload file to Supabase')
        ->and($disk->exists($this->testPath))->toBeTrue('Uploaded file does not exist on Supabase')
        ->and($disk->get($this->testPath))->toBe($this->testContent, 'File content does not match');
})->depends('supabase environment variables are properly configured');

test('can generate url for file on supabase', function (): void {
    $disk = Storage::disk('supabase');

    // Upload test file
    $disk->put($this->testPath, $this->testContent);

    // Test URL generation
    $url = $disk->url($this->testPath);

    // Debug the URL and bucket name
    info('Generated URL: '.$url);
    info('Bucket name: '.env('SUPABASE_STORAGE_BUCKET'));

    expect($url)
        ->not->toBeEmpty('Failed to generate URL')
        // Check for the standard Supabase URL structure (case-sensitive)
        ->toContain('/storage/v1/object/public/', 'URL does not follow Supabase format')
        // Check that the file path is in the URL
        ->toContain($this->testPath, 'URL does not contain the file path')
        // Check that it's a valid URL
        ->toMatch('/^https?:\/\/.+/i', 'URL is not properly formatted');
})->depends('can write and read file on supabase');

test('can delete file from supabase', function (): void {
    $disk = Storage::disk('supabase');

    // Upload and then delete test file
    $disk->put($this->testPath, $this->testContent);
    $deleted = $disk->delete($this->testPath);

    expect($deleted)->toBeTrue('Failed to delete file from Supabase')
        ->and($disk->exists($this->testPath))->toBeFalse('File still exists after deletion');
})->depends('can write and read file on supabase');

test('can upload large file to supabase', function (): void {
    $disk = Storage::disk('supabase');

    // Create a smaller test file (1MB instead of 5MB)
    $largeFile = UploadedFile::fake()->create('large-file.txt', 1024); // 1MB
    $largePath = 'test/large-'.Str::random(10).'.txt';

    try {
        // Upload large file with direct put() instead of putFileAs()
        $stream = fopen($largeFile->getRealPath(), 'r');
        $uploaded = $disk->put($largePath, $stream);
        if (is_resource($stream)) {
            fclose($stream);
        }

        expect($uploaded)->toBeTrue('Failed to upload large file to Supabase')
            ->and($disk->exists($largePath))->toBeTrue('Large file does not exist on Supabase');

        // Cleanup
        $disk->delete($largePath);
    } catch (\Exception $e) {
        test()->fail('Failed to handle large file: '.$e->getMessage());
    }
})->depends('supabase environment variables are properly configured');

test('handles concurrent uploads to supabase', function (): void {
    $disk = Storage::disk('supabase');
    $files = [];

    try {
        // Create and upload 3 files concurrently
        for ($i = 0; $i < 3; $i++) {
            $path = "test/concurrent-{$i}-".Str::random(10).'.txt';
            $files[] = $path;

            $uploaded = $disk->put($path, "Concurrent test content {$i}");
            expect($uploaded)->toBeTrue("Failed to upload concurrent file {$i}");
        }

        // Verify all files exist
        foreach ($files as $file) {
            expect($disk->exists($file))->toBeTrue("Concurrent file {$file} does not exist");
        }
    } finally {
        // Cleanup
        foreach ($files as $file) {
            $disk->delete($file);
        }
    }
})->depends('supabase environment variables are properly configured');

test('handles file metadata on supabase', function (): void {
    $disk = Storage::disk('supabase');

    // Upload file with metadata
    $metadata = [
        'Content-Type' => 'text/plain',
        'Content-Disposition' => 'attachment; filename="test.txt"',
    ];

    $uploaded = $disk->put($this->testPath, $this->testContent, $metadata);

    expect($uploaded)->toBeTrue('Failed to upload file with metadata')
        ->and($disk->exists($this->testPath))->toBeTrue('File with metadata does not exist');

    // Get file URL and verify it's accessible
    $url = $disk->url($this->testPath);
    expect($url)->not->toBeEmpty('Failed to generate URL for file with metadata');
})->depends('supabase environment variables are properly configured');
