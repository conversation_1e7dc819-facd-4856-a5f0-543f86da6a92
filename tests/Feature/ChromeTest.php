<?php

use HeadlessChromium\BrowserFactory;
use Symfony\Component\Filesystem\Filesystem;

/**
 * Feature test to check if chrome-php/chrome package is working.
 *
 * This test will:
 * 1. Launch a headless Chrome browser using chrome-php/chrome.
 * 2. Create a new page and navigate to example.com.
 * 3. Take a screenshot of the page.
 * 4. Assert that the screenshot file was created successfully.
 * 5. Clean up the screenshot file after the test.
 */
test('chrome-php/chrome is working', function (): void {
    // putenv('CHROME_PATH=/sbin/google-chrome-stable'); // Set CHROME_PATH here
    // Create a BrowserFactory instance
    $browserFactory = new BrowserFactory;

    // Launch a headless Chrome browser
    $browser = $browserFactory->createBrowser();

    // Define the path for the screenshot file
    $screenshotPath = storage_path('app/test-screenshot.png');

    try {
        // Create a new page
        $page = $browser->createPage();

        // Navigate to example.com and wait for navigation to complete
        $page->navigate('http://example.com')->waitForNavigation();

        // Take a screenshot and save it to the specified path
        $page->screenshot()->saveToFile($screenshotPath);

        // Assert that the screenshot file exists
        $fileSystem = new Filesystem;
        expect($fileSystem->exists($screenshotPath))->toBeTrue('Screenshot file was not created.');

    } catch (\Exception $e) {
        // If any exception occurs during the process, fail the test
        test()->fail('Chrome test failed: '.$e->getMessage());
    } finally {
        // Close the browser
        $browser->close();

        // Clean up: Delete the screenshot file after the test
        if (isset($fileSystem) && $fileSystem->exists($screenshotPath)) {
            $fileSystem->remove($screenshotPath);
        }
    }
});
