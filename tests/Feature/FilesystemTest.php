<?php

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;

beforeEach(function (): void {
    // Create a test file for each test
    $this->testFile = UploadedFile::fake()->create('test.txt', 100);
    $this->testContent = 'Testing filesystem at '.now()->toDateTimeString();
});

afterEach(function (): void {
    // Cleanup test files
    Storage::disk('local')->delete('test/test.txt');
    Storage::disk('local')->deleteDirectory('test');
});

test('filesystem environment variables are properly configured', function (): void {
    $defaultDisk = env('FILESYSTEM_DISK');

    expect($defaultDisk)
        ->not->toBeNull('FILESYSTEM_DISK is not set in .env')
        ->not->toBeEmpty('FILESYSTEM_DISK is empty in .env');

    // Verify the configured disk exists in config
    $availableDisks = Config::get('filesystems.disks');
    expect($availableDisks)
        ->toHaveKey($defaultDisk);

    expect(Config::get("filesystems.disks.{$defaultDisk}"))
        ->not->toBeNull("Configured disk '$defaultDisk' is not defined in filesystems config");
});

test('local filesystem driver is properly configured and working', function (): void {
    $disk = Storage::disk('local');
    $testContent = 'This is a test content: '.now()->toDateTimeString();

    // Test file upload using put()
    $uploaded = $disk->put('test/test.txt', $testContent);

    expect($uploaded)->toBeTrue('Failed to upload file to local disk using put()')
        ->and($disk->exists('test/test.txt'))->toBeTrue('Uploaded file does not exist on local disk')
        ->and($disk->get('test/test.txt'))->not()->toBeEmpty('Failed to read file from local disk')
        ->and($disk->delete('test/test.txt'))->toBeTrue('Failed to delete file from local disk')
        ->and($disk->missing('test/test.txt'))->toBeTrue('File still exists after deletion');
})->depends('filesystem environment variables are properly configured');

test('public filesystem driver is properly configured and working', function (): void {
    // Skip if public disk is not configured
    $publicDiskConfig = Config::get('filesystems.disks.public');
    expect($publicDiskConfig)->not()->toBeNull('Public disk is not configured');

    $disk = Storage::disk('public');
    $testContent = 'Public disk test content: '.now()->toDateTimeString();

    // Test file upload using put() - simplified for public disk
    $uploaded = $disk->put('test/test.txt', $testContent);

    expect($uploaded)->toBeTrue('Failed to upload file to public disk using put()')
        ->and($disk->exists('test/test.txt'))->toBeTrue('Uploaded file does not exist on public disk')
        ->and($disk->url('test/test.txt'))->not()->toBeEmpty('Failed to generate URL for public file')
        ->and(parse_url($disk->url('test/test.txt'), PHP_URL_PATH))->toBe('/storage/test/test.txt', 'Invalid public URL path')
        ->and($disk->delete('test/test.txt'))->toBeTrue('Failed to delete file from public disk')
        ->and($disk->missing('test/test.txt'))->toBeTrue('File still exists after deletion');
})->skip(fn () => ! Config::get('filesystems.disks.public'), 'Public disk is not configured');

test('s3 filesystem driver is properly configured and working', function (): void {
    // Verify S3 configuration
    $requiredS3Vars = [
        'AWS_ACCESS_KEY_ID' => env('AWS_ACCESS_KEY_ID'),
        'AWS_SECRET_ACCESS_KEY' => env('AWS_SECRET_ACCESS_KEY'),
        'AWS_DEFAULT_REGION' => env('AWS_DEFAULT_REGION'),
        'AWS_BUCKET' => env('AWS_BUCKET'),
    ];

    foreach ($requiredS3Vars as $key => $value) {
        expect($value)
            ->not->toBeNull("$key is not set")
            ->not->toBeEmpty("$key is empty");
    }

    $disk = Storage::disk('s3');

    // Test file upload
    $uploaded = $disk->putFileAs('test', $this->testFile, 'test.txt');
    expect($uploaded)->toBeTrue('Failed to upload file to S3')
        ->and($disk->exists('test/test.txt'))->toBeTrue('Uploaded file does not exist on S3');

    // Test file URL generation
    $url = $disk->url('test/test.txt');
    expect($url)
        ->not->toBeEmpty('Failed to generate S3 URL')
        ->toContain($requiredS3Vars['AWS_BUCKET'], 'Invalid S3 URL format');

    // Test file deletion
    $deleted = $disk->delete('test/test.txt');
    expect($deleted)->toBeTrue('Failed to delete file from S3')
        ->and($disk->exists('test/test.txt'))->toBeFalse('File still exists after deletion');
})->skip(fn () => ! env('AWS_ACCESS_KEY_ID'), 'S3 is not configured');
