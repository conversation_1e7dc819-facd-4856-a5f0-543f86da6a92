<?php

declare(strict_types=1);

namespace Tests\Feature;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use RuntimeException;
use Spatie\Browsershot\Browsershot;
use Tests\TestCase;

class BrowsershotTest extends TestCase
{
    private string $chromePath;

    protected function setUp(): void
    {
        parent::setUp();

        // Check for Windows Chrome paths first
        $windowsPaths = [
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe',
        ];

        // Common paths for Chrome/Chromium on Linux
        $linuxPaths = [
            '/usr/bin/google-chrome-stable',
            '/usr/bin/google-chrome',
            '/usr/bin/chromium',
            '/usr/bin/chromium-browser',
            '/snap/bin/chromium',
            '/opt/google/chrome/google-chrome',
        ];

        // Combine paths based on OS
        $possiblePaths = PHP_OS_FAMILY === 'Windows' ? $windowsPaths : $linuxPaths;

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                $this->chromePath = $path;
                break;
            }
        }

        if (! isset($this->chromePath)) {
            // Log the current user and attempted paths for debugging
            Log::error('Chrome search paths: '.implode(', ', $possiblePaths));
            Log::error('Current user: '.get_current_user());
            throw new RuntimeException('Chrome/Chromium not found. Please install Chrome browser.');
        }
    }

    public function test_browsershot_configuration()
    {
        try {
            // Test Bun installation instead of Node.js
            $bunVersion = trim(shell_exec('bun -v'));
            $this->assertNotEmpty($bunVersion, 'Bun is not installed');
            Log::info('Bun version: '.$bunVersion);

            // Test Chrome installation
            $this->assertFileExists($this->chromePath, 'Chrome browser not found');
            Log::info('Chrome exists at: '.$this->chromePath);

            // Test if Chrome is executable
            $this->assertTrue(is_executable($this->chromePath), 'Chrome is not executable');
            Log::info('Chrome is executable');

            // Test puppeteer installation with Bun
            $this->assertTrue(file_exists(base_path('node_modules/puppeteer/package.json')), 'Puppeteer is not installed');
            Log::info('Puppeteer is installed');

            // Test temp directory permissions
            $tempDir = storage_path('app/temp');
            if (! file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            $this->assertTrue(is_writable($tempDir), 'Temp directory is not writable');
            Log::info('Temp directory is writable');

            // Test if we can create a file in temp
            $testFile = $tempDir.'/browsershot_test_'.uniqid();
            $this->assertTrue(file_put_contents($testFile, 'test') !== false, 'Cannot write to temp directory');
            if (file_exists($testFile)) {
                unlink($testFile);
            }
            Log::info('Successfully tested temp directory write access');

        } catch (\Exception $e) {
            Log::error('Configuration test failed: '.$e->getMessage());
            Log::error('Stack trace: '.$e->getTraceAsString());
            $this->markTestFailed('Configuration test failed: '.$e->getMessage());
        }
    }

    public function test_chrome_php_direct()
    {
        // Test Chrome-PHP library directly
        try {
            // Create test directory if it doesn't exist
            $testDir = storage_path('app/test');
            if (! file_exists($testDir)) {
                mkdir($testDir, 0755, true);
            }

            // Create custom Chrome data directory
            $chromeUserDataDir = storage_path('app/chrome-data-test');
            if (! file_exists($chromeUserDataDir)) {
                mkdir($chromeUserDataDir, 0755, true);
            }
            chmod($chromeUserDataDir, 0777);

            // Create subdirectories that Chrome might need
            mkdir($chromeUserDataDir.'/Default', 0777, true);
            mkdir($chromeUserDataDir.'/cache', 0777, true);
            mkdir($chromeUserDataDir.'/Crashpad', 0777, true);
            mkdir($chromeUserDataDir.'/.local/share/applications', 0777, true);
            touch($chromeUserDataDir.'/.local/share/applications/mimeapps.list');

            $outputPath = $testDir.'/chrome_php_test.pdf';

            Log::info('Starting Chrome-PHP test', [
                'chrome_path' => $this->chromePath,
                'output_path' => $outputPath,
                'chrome_user_data_dir' => $chromeUserDataDir,
            ]);

            // Initialize BrowserFactory with custom options
            $browserFactory = new \HeadlessChromium\BrowserFactory;
            $browser = $browserFactory->createBrowser([
                'userDataDir' => $chromeUserDataDir,
                'ignoreCertificateErrors' => true,
                'noSandbox' => true,
                'keepAlive' => false,
                'sendSyncDefaultTimeout' => 45000,
                'windowSize' => [1024, 768],
                'enableImages' => false,
                'debugLogger' => true,
                'args' => [
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-software-rasterizer',
                    '--disable-default-apps',
                    '--disable-setuid-sandbox',
                    '--no-first-run',
                    '--no-sandbox',
                    '--no-zygote',
                    '--single-process',
                    '--homedir='.$chromeUserDataDir,
                    '--crash-dumps-dir='.$chromeUserDataDir,
                    '--user-data-dir='.$chromeUserDataDir,
                    '--disable-crashpad',
                ],
            ]);

            Log::info('Browser instance created successfully');

            // Create a page and set HTML content
            $page = $browser->createPage();
            $html = '<!DOCTYPE html><html><head><title>Chrome-PHP Test</title></head><body><h1>Test PDF Generation with Chrome-PHP</h1><p>Generated at: '.now().'</p></body></html>';
            $page->setHtml($html);

            Log::info('Page created and HTML content set');

            // Generate PDF and save to file
            $pdf = $page->pdf(['printBackground' => true]);
            $pdf->saveToFile($outputPath);

            Log::info('PDF generated and saved', ['path' => $outputPath]);

            // Assert the PDF was created
            $this->assertFileExists($outputPath);
            $this->assertGreaterThan(0, filesize($outputPath));

            // Clean up
            $browser->close();
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }

            Log::info('Chrome-PHP test completed successfully');

        } catch (\Exception $e) {
            Log::error('Chrome-PHP test failed: '.$e->getMessage());
            Log::error('Stack trace: '.$e->getTraceAsString());
            $this->markTestFailed('Chrome-PHP test failed: '.$e->getMessage());
        }
    }

    public function test_can_generate_pdf_from_html()
    {
        // Skip this test if running on Windows
        if (PHP_OS_FAMILY === 'Windows') {
            $this->markTestSkipped('This test is for Linux environments only.');
        }

        // Create test directory if it doesn't exist
        $testDir = storage_path('app/test');
        if (! file_exists($testDir)) {
            mkdir($testDir, 0755, true);
        }

        // Create temp directory if it doesn't exist
        $tempDir = storage_path('app/temp');
        if (! file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $outputPath = storage_path('app/test/test.pdf');

        // Simple HTML content for testing
        $html = '<!DOCTYPE html><html><head><title>Test PDF</title><style>body { font-family: Arial, sans-serif; }</style></head><body><h1>Test PDF Generation</h1><p>This is a test PDF generated with Browsershot.</p><p>Generated at: '.now().'</p></body></html>';

        try {
            // Log debug information
            Log::info('Starting PDF generation test');
            Log::info('Temp Directory: '.$tempDir);
            Log::info('Output Path: '.$outputPath);

            // Configure Browsershot
            $browsershot = Browsershot::html($html);

            // Update configuration to use Bun
            $browsershot->setBinPath(base_path('node_modules/.bin'))
                ->setChromePath($this->chromePath)
                ->setCustomTempPath($tempDir)
                ->noSandbox()
                ->ignoreHttpsErrors()
                ->setOption('args', [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--headless=new',
                ])
                ->setOption('viewport', [
                    'width' => 1920,
                    'height' => 1080,
                ])
                ->setOption('displayHeaderFooter', false)
                ->setOption('format', 'A4')
                ->setOption('margin', [
                    'top' => '10mm',
                    'right' => '10mm',
                    'bottom' => '10mm',
                    'left' => '10mm',
                ])
                ->setOption('printBackground', true)
                ->timeout(120000);

            Log::info('Browsershot configured, attempting to generate PDF');

            // Save the PDF
            $browsershot->savePdf($outputPath);
            Log::info('PDF generation completed');

            // Assert the PDF was created
            $this->assertFileExists($outputPath);
            $this->assertGreaterThan(0, filesize($outputPath));
            Log::info('PDF file exists and has content');

            // Clean up
            if (file_exists($outputPath)) {
                unlink($outputPath);
                Log::info('PDF file cleaned up');
            }

            // Clean up temp directory
            if (is_dir($tempDir)) {
                $files = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($tempDir, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::CHILD_FIRST
                );
                foreach ($files as $file) {
                    if ($file->isDir()) {
                        rmdir($file->getRealPath());
                    } else {
                        unlink($file->getRealPath());
                    }
                }
                rmdir($tempDir);
                Log::info('Temp directory cleaned up');
            }

        } catch (\Exception $e) {
            Log::error('PDF Generation failed: '.$e->getMessage());
            Log::error('Stack trace: '.$e->getTraceAsString());

            // Clean up temp directory on error
            if (is_dir($tempDir)) {
                $files = new \RecursiveIteratorIterator(
                    new \RecursiveDirectoryIterator($tempDir, \RecursiveDirectoryIterator::SKIP_DOTS),
                    \RecursiveIteratorIterator::CHILD_FIRST
                );
                foreach ($files as $file) {
                    if ($file->isDir()) {
                        rmdir($file->getRealPath());
                    } else {
                        unlink($file->getRealPath());
                    }
                }
                rmdir($tempDir);
            }

            $this->fail('Failed to generate PDF: '.$e->getMessage()."\n".$e->getTraceAsString());
        }
    }

    public function test_can_generate_pdf_from_html_windows()
    {
        // Skip this test if not running on Windows
        if (PHP_OS_FAMILY !== 'Windows') {
            $this->markTestSkipped('This test is for Windows environments only.');
        }

        // Create test directory if it doesn't exist
        $testDir = storage_path('app/test');
        if (! file_exists($testDir)) {
            mkdir($testDir, 0755, true);
        }

        // Create temp directory if it doesn't exist
        $tempDir = storage_path('app/temp');
        if (! file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        $outputPath = storage_path('app/test/test-windows.pdf');

        // Simple HTML content for testing
        $html = '<!DOCTYPE html><html><head><title>Test PDF</title><style>body { font-family: Arial, sans-serif; }</style></head><body><h1>Test PDF Generation</h1><p>This is a test PDF generated with Browsershot on Windows.</p><p>Generated at: '.now().'</p></body></html>';

        try {
            // Log debug information
            Log::info('Starting PDF generation test (Windows)');
            Log::info('Temp Directory: '.$tempDir);
            Log::info('Output Path: '.$outputPath);

            // Find Node.js executable
            $nodePath = 'C:\\Program Files\\nodejs\\node.exe';
            if (! file_exists($nodePath)) {
                $nodePath = 'C:\\Program Files (x86)\\nodejs\\node.exe';
            }

            if (! file_exists($nodePath)) {
                // Try to find node in PATH
                $output = shell_exec('where node');
                if ($output) {
                    $nodePath = trim(explode("\n", $output)[0]);
                }
            }

            if (! file_exists($nodePath)) {
                $this->markTestSkipped('Node.js not found. Please install Node.js');
            }

            // Configure Browsershot
            $browsershot = Browsershot::html($html);

            // Update configuration to use explicit Node path
            $browsershot
                ->setNodeBinary($nodePath)
                ->setChromePath($this->chromePath)
                ->setCustomTempPath($tempDir)
                ->noSandbox()
                ->ignoreHttpsErrors()
                ->setOption('args', [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-gpu',
                    '--disable-dev-shm-usage',
                    '--headless=new',
                ])
                ->setOption('viewport', [
                    'width' => 1920,
                    'height' => 1080,
                ])
                ->setOption('displayHeaderFooter', false)
                ->setOption('format', 'A4')
                ->setOption('margin', [
                    'top' => '10mm',
                    'right' => '10mm',
                    'bottom' => '10mm',
                    'left' => '10mm',
                ])
                ->setOption('printBackground', true)
                ->timeout(120000);

            Log::info('Browsershot configured for Windows, attempting to generate PDF');

            // Save the PDF
            $browsershot->savePdf($outputPath);
            Log::info('PDF generation completed');

            // Assert the PDF was created
            $this->assertFileExists($outputPath);
            $this->assertGreaterThan(0, filesize($outputPath));
            Log::info('PDF file exists and has content');

            // Clean up
            if (file_exists($outputPath)) {
                unlink($outputPath);
                Log::info('PDF file cleaned up');
            }

        } catch (\Exception $e) {
            Log::error('PDF Generation failed (Windows): '.$e->getMessage());
            Log::error('Stack trace: '.$e->getTraceAsString());
            $this->fail('Failed to generate PDF on Windows: '.$e->getMessage());
        }
    }

    public function test_can_generate_pdf(): void
    {
        $nodePath = 'C:\\Program Files\\nodejs\\node.exe';
        if (! file_exists($nodePath)) {
            $nodePath = 'C:\\Program Files (x86)\\nodejs\\node.exe';
        }

        if (! file_exists($nodePath)) {
            // Try to find node in PATH
            $output = shell_exec('where node');
            if ($output) {
                $nodePath = trim(explode("\n", $output)[0]);
            }
        }

        if (! file_exists($nodePath)) {
            $this->markTestSkipped('Node.js not found. Please install Node.js');
        }

        $outputPath = storage_path('app/test.pdf');

        // Clean up any existing test file
        if (file_exists($outputPath)) {
            unlink($outputPath);
        }

        $result = Browsershot::url('https://example.com')
            ->setNodeBinary($nodePath)
            ->setChromePath($this->chromePath)
            ->noSandbox()
            ->pdf();

        // Save the PDF to verify it was generated
        file_put_contents($outputPath, $result);

        $this->assertFileExists($outputPath);
        $this->assertGreaterThan(0, filesize($outputPath));

        // Clean up
        unlink($outputPath);
    }

    /**
     * Test PDF generation and storage on local filesystem
     */
    public function test_can_store_and_delete_pdf_on_local_disk(): void
    {
        $pdfContent = Browsershot::url('https://example.com')
            ->setChromePath($this->chromePath)
            ->noSandbox()
            ->pdf();

        $path = 'pdfs/test.pdf';

        // Test upload
        Storage::disk('local')->put($path, $pdfContent);
        $this->assertTrue(Storage::disk('local')->exists($path));

        // Test file size
        $this->assertGreaterThan(0, Storage::disk('local')->size($path));

        // Test deletion
        Storage::disk('local')->delete($path);
        $this->assertFalse(Storage::disk('local')->exists($path));
    }

    /**
     * Test PDF generation and storage on public disk
     */
    public function test_can_store_and_delete_pdf_on_public_disk(): void
    {
        $pdfContent = Browsershot::url('https://example.com')
            ->setChromePath($this->chromePath)
            ->noSandbox()
            ->pdf();

        $path = 'pdfs/test.pdf';

        // Test upload
        Storage::disk('public')->put($path, $pdfContent);
        $this->assertTrue(Storage::disk('public')->exists($path));

        // Test URL generation
        $this->assertNotEmpty(Storage::disk('public')->url($path));

        // Test deletion
        Storage::disk('public')->delete($path);
        $this->assertFalse(Storage::disk('public')->exists($path));
    }

    /**
     * Test PDF generation and storage on Supabase
     */
    public function test_can_store_and_delete_pdf_on_supabase(): void
    {
        // Skip if Supabase credentials are not configured
        if (empty(config('filesystems.disks.supabase.key'))) {
            $this->markTestSkipped('Supabase is not configured.');
        }

        $pdfContent = Browsershot::url('https://example.com')
            ->setChromePath($this->chromePath)
            ->noSandbox()
            ->pdf();

        $path = 'pdfs/test.pdf';

        try {
            // Test upload
            Storage::disk('supabase')->put($path, $pdfContent);
            $this->assertTrue(Storage::disk('supabase')->exists($path));

            // Test URL generation
            $this->assertNotEmpty(Storage::disk('supabase')->url($path));

            // Test deletion
            Storage::disk('supabase')->delete($path);
            $this->assertFalse(Storage::disk('supabase')->exists($path));
        } catch (\Exception $e) {
            $this->fail('Supabase storage test failed: '.$e->getMessage());
        }
    }

    /**
     * Test PDF generation and storage on Cloudflare R2
     */

    /**
     * Test PDF generation and storage on Assessment Supabase
     */
    public function test_can_store_and_delete_pdf_on_assessment_supabase(): void
    {
        // Skip if Assessment Supabase credentials are not configured
        if (empty(config('filesystems.disks.assesment_supabase.key'))) {
            $this->markTestSkipped('Assessment Supabase is not configured.');
        }

        $pdfContent = Browsershot::url('https://example.com')
            ->setChromePath($this->chromePath)
            ->noSandbox()
            ->pdf();

        $path = 'pdfs/test.pdf';

        try {
            // Test upload
            Storage::disk('assesment_supabase')->put($path, $pdfContent);
            $this->assertTrue(Storage::disk('assesment_supabase')->exists($path));

            // Test URL generation
            $this->assertNotEmpty(Storage::disk('assesment_supabase')->url($path));

            // Test deletion
            Storage::disk('assesment_supabase')->delete($path);
            $this->assertFalse(Storage::disk('assesment_supabase')->exists($path));
        } catch (\Exception $e) {
            $this->fail('Assessment Supabase storage test failed: '.$e->getMessage());
        }
    }
}
