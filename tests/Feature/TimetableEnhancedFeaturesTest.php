<?php

namespace Tests\Feature;

use App\Filament\Pages\Timetable;
use App\Models\Classes;
use App\Models\Faculty;
use App\Models\Room;
use App\Models\Schedule;
use App\Models\Subject;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class TimetableEnhancedFeaturesTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_calculates_schedule_position_correctly()
    {
        $timetablePage = new Timetable;

        // Create a mock schedule from 8:30 AM to 10:15 AM
        $schedule = (object) [
            'start_time' => '08:30:00',
            'end_time' => '10:15:00',
        ];

        $position = $timetablePage->calculateSchedulePosition($schedule);

        // 8:30 AM = 1.5 hours from 7:00 AM base = 1.5 * 80 = 120px
        $this->assertEquals(120, $position['top']);

        // Duration: 1 hour 45 minutes = 1.75 hours = 1.75 * 80 = 140px
        $this->assertEquals(140, $position['height']);
    }

    /** @test */
    public function it_handles_minimum_height_for_short_schedules()
    {
        $timetablePage = new Timetable;

        // Create a very short schedule (15 minutes)
        $schedule = (object) [
            'start_time' => '09:00:00',
            'end_time' => '09:15:00',
        ];

        $position = $timetablePage->calculateSchedulePosition($schedule);

        // Should enforce minimum height of 40px
        $this->assertEquals(40, $position['height']);
    }

    /** @test */
    public function it_provides_time_slots_for_positioning()
    {
        $timetablePage = new Timetable;
        $slots = $timetablePage->getTimeSlotsForPositioning();

        $this->assertIsArray($slots);
        $this->assertContains('07:00', $slots);
        $this->assertContains('21:00', $slots);
        $this->assertCount(15, $slots); // 7 AM to 9 PM = 15 slots
    }

    /** @test */
    public function it_renders_timetable_page_with_enhanced_features()
    {
        // Create test data
        $room = Room::factory()->create(['name' => 'Test Room']);
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'college',
        ]);

        $schedule = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class->id,
            'day_of_week' => 'Monday',
            'start_time' => '08:00:00',
            'end_time' => '10:00:00',
        ]);

        $component = Livewire::test(Timetable::class)
            ->set('selectedView', 'room')
            ->set('selectedId', $room->id);

        $component->assertSee('Test Room');
        $component->assertSee('Room Schedule');
    }

    /** @test */
    public function it_handles_loading_states_correctly()
    {
        $room = Room::factory()->create();

        $component = Livewire::test(Timetable::class);

        // Test that updating selectedId triggers loading
        $component->set('selectedView', 'room')
            ->set('selectedId', $room->id);

        // Should dispatch loading events
        $component->assertDispatched('show-loading');
        $component->assertDispatched('hide-loading');
    }

    /** @test */
    public function it_supports_different_schedule_classifications()
    {
        $room = Room::factory()->create();
        $faculty = Faculty::factory()->create();
        $subject1 = Subject::factory()->create();
        $subject2 = Subject::factory()->create();

        // Create college class
        $collegeClass = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject1->code,
            'classification' => 'college',
        ]);

        // Create SHS class
        $shsClass = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject2->code,
            'classification' => 'shs',
        ]);

        $collegeSchedule = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $collegeClass->id,
            'day_of_week' => 'Monday',
            'start_time' => '08:00:00',
            'end_time' => '09:00:00',
        ]);

        $shsSchedule = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $shsClass->id,
            'day_of_week' => 'Tuesday',
            'start_time' => '08:00:00',
            'end_time' => '09:00:00',
        ]);

        $component = Livewire::test(Timetable::class)
            ->set('selectedView', 'room')
            ->set('selectedId', $room->id);

        // Should show both classifications
        $component->assertSee('COLLEGE');
        $component->assertSee('SHS');
    }

    /** @test */
    public function it_handles_long_duration_schedules()
    {
        $timetablePage = new Timetable;

        // Create a long schedule from 8 AM to 1 PM (5 hours)
        $schedule = (object) [
            'start_time' => '08:00:00',
            'end_time' => '13:00:00',
        ];

        $position = $timetablePage->calculateSchedulePosition($schedule);

        // 8:00 AM = 1 hour from 7:00 AM base = 1 * 80 = 80px
        $this->assertEquals(80, $position['top']);

        // Duration: 5 hours = 5 * 80 = 400px
        $this->assertEquals(400, $position['height']);
    }

    /** @test */
    public function it_handles_schedules_with_minutes()
    {
        $timetablePage = new Timetable;

        // Create a schedule from 9:15 AM to 10:45 AM
        $schedule = (object) [
            'start_time' => '09:15:00',
            'end_time' => '10:45:00',
        ];

        $position = $timetablePage->calculateSchedulePosition($schedule);

        // 9:15 AM = 2.25 hours from 7:00 AM base = 2.25 * 80 = 180px
        $this->assertEquals(180, $position['top']);

        // Duration: 1.5 hours = 1.5 * 80 = 120px
        $this->assertEquals(120, $position['height']);
    }

    /** @test */
    public function it_provides_enhanced_view_data()
    {
        $room = Room::factory()->create();
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
        ]);

        $schedule = Schedule::factory()->create([
            'room_id' => $room->id,
            'class_id' => $class->id,
        ]);

        $component = Livewire::test(Timetable::class)
            ->set('selectedView', 'room')
            ->set('selectedId', $room->id);

        $viewData = $component->instance()->getViewData();

        $this->assertArrayHasKey('schedules', $viewData);
        $this->assertArrayHasKey('timeSlots', $viewData);
        $this->assertArrayHasKey('days', $viewData);
    }

    /** @test */
    public function it_handles_edge_case_times()
    {
        $timetablePage = new Timetable;

        // Test early morning schedule (7:00 AM exactly)
        $earlySchedule = (object) [
            'start_time' => '07:00:00',
            'end_time' => '08:00:00',
        ];

        $position = $timetablePage->calculateSchedulePosition($earlySchedule);
        $this->assertEquals(0, $position['top']); // Should be at the top

        // Test late evening schedule
        $lateSchedule = (object) [
            'start_time' => '20:00:00',
            'end_time' => '21:00:00',
        ];

        $position = $timetablePage->calculateSchedulePosition($lateSchedule);
        $this->assertEquals(1040, $position['top']); // 13 hours * 80px = 1040px
    }
}
