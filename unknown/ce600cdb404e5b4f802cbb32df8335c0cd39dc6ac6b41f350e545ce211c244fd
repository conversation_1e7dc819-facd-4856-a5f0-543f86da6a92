<?php

namespace Tests\Feature;

use App\Models\Classes;
use App\Models\Faculty;
use App\Models\Schedule;
use App\Models\Subject;
use App\Services\GeneralSettingsService;
use App\Services\TimetableConflictService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TimetableColorCodingTest extends TestCase
{
    use RefreshDatabase;

    protected TimetableConflictService $conflictService;

    protected function setUp(): void
    {
        parent::setUp();

        $settingsService = $this->app->make(GeneralSettingsService::class);
        $this->conflictService = new TimetableConflictService($settingsService);
    }

    /** @test */
    public function it_returns_correct_css_class_for_college_courses()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'college',
        ]);

        $schedule = Schedule::factory()->create([
            'class_id' => $class->id,
        ]);

        $cssClass = $this->conflictService->getScheduleCssClass($schedule, false);
        $this->assertEquals('schedule-college', $cssClass);
    }

    /** @test */
    public function it_returns_correct_css_class_for_shs_courses()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'shs',
        ]);

        $schedule = Schedule::factory()->create([
            'class_id' => $class->id,
        ]);

        $cssClass = $this->conflictService->getScheduleCssClass($schedule, false);
        $this->assertEquals('schedule-shs', $cssClass);
    }

    /** @test */
    public function it_returns_conflict_css_class_when_conflict_exists()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'college',
        ]);

        $schedule = Schedule::factory()->create([
            'class_id' => $class->id,
        ]);

        $cssClass = $this->conflictService->getScheduleCssClass($schedule, true);
        $this->assertEquals('schedule-conflict', $cssClass);
    }

    /** @test */
    public function it_returns_default_css_class_for_null_classification()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => null,
        ]);

        $schedule = Schedule::factory()->create([
            'class_id' => $class->id,
        ]);

        $cssClass = $this->conflictService->getScheduleCssClass($schedule, false);
        $this->assertEquals('schedule-college', $cssClass); // Default to college
    }

    /** @test */
    public function it_returns_correct_hex_colors_for_college()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'college',
        ]);

        $schedule = Schedule::factory()->create([
            'class_id' => $class->id,
        ]);

        $colors = $this->conflictService->getScheduleColorCode($schedule);

        $this->assertEquals('#dbeafe', $colors['bg']);
        $this->assertEquals('#bfdbfe', $colors['border']);
        $this->assertEquals('#1e40af', $colors['text']);
        $this->assertEquals('schedule-college', $colors['class']);
    }

    /** @test */
    public function it_returns_correct_hex_colors_for_shs()
    {
        $faculty = Faculty::factory()->create();
        $subject = Subject::factory()->create();

        $class = Classes::factory()->create([
            'faculty_id' => $faculty->id,
            'subject_code' => $subject->code,
            'classification' => 'shs',
        ]);

        $schedule = Schedule::factory()->create([
            'class_id' => $class->id,
        ]);

        $colors = $this->conflictService->getScheduleColorCode($schedule);

        $this->assertEquals('#dcfce7', $colors['bg']);
        $this->assertEquals('#bbf7d0', $colors['border']);
        $this->assertEquals('#166534', $colors['text']);
        $this->assertEquals('schedule-shs', $colors['class']);
    }

    /** @test */
    public function it_handles_schedule_without_class()
    {
        $schedule = Schedule::factory()->create([
            'class_id' => null,
        ]);

        $cssClass = $this->conflictService->getScheduleCssClass($schedule, false);
        $this->assertEquals('schedule-default', $cssClass);

        $colors = $this->conflictService->getScheduleColorCode($schedule);
        $this->assertEquals('#f3f4f6', $colors['bg']);
        $this->assertEquals('schedule-default', $colors['class']);
    }
}
