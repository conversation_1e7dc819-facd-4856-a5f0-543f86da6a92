<?php

namespace Tests\Feature\Api;

use App\Models\GeneralSetting;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GeneralSettingsApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_retrieve_general_settings()
    {
        // Arrange
        $settings = GeneralSetting::create([
            'school_starting_date' => Carbon::create(2023, 8, 1),
            'school_ending_date' => Carbon::create(2024, 5, 31),
            'semester' => 1,
            'school_portal_url' => 'https://test-portal.edu',
            'school_portal_enabled' => true,
            'online_enrollment_enabled' => true,
            'features' => [
                'enable_grades' => true,
                'enable_enrollment' => true,
            ],
            'curriculum_year' => '2023',
        ]);

        // Act
        $response = $this->getJson('/api/v1/settings');

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'school_year',
                    'school_year_string',
                    'semester',
                    'school_portal_url',
                    'school_portal_enabled',
                    'online_enrollment_enabled',
                    'features',
                    'curriculum_year',
                ],
            ])
            ->assertJson([
                'data' => [
                    'school_year' => '2023-2024',
                    'school_year_string' => '2023 - 2024',
                    'semester' => '1st Semester',
                    'school_portal_url' => 'https://test-portal.edu',
                    'school_portal_enabled' => true,
                    'online_enrollment_enabled' => true,
                    'curriculum_year' => '2023',
                ],
            ]);
    }
}
