<?php

namespace App\Forms\Components;

use App\Models\Subject;
use Filament\Forms\Components\Concerns\HasOptions;
use Filament\Forms\Components\Concerns\HasPlaceholder;
use Filament\Forms\Components\Field;

class SubjectSelect extends Field
{
    use HasOptions;
    use HasPlaceholder;

    protected string $view = 'filament.forms.components.subject-select';

    protected bool $isSearchable = false;

    protected bool $isHighlightEnabled = true;

    protected bool $isDisableEmptyOptionsEnabled = true;

    protected bool $autoRetrieveSelectedOptionLabel = true;

    public function searchable(bool $condition = true): static
    {
        $this->isSearchable = $condition;

        return $this;
    }

    public function highlightOptions(bool $condition = true): static
    {
        $this->isHighlightEnabled = $condition;

        return $this;
    }

    public function disableEmptyOptions(bool $condition = true): static
    {
        $this->isDisableEmptyOptionsEnabled = $condition;

        return $this;
    }

    public function autoRetrieveSelectedOptionLabel(bool $condition = true): static
    {
        $this->autoRetrieveSelectedOptionLabel = $condition;

        return $this;
    }

    public function isSearchable(): bool
    {
        return $this->isSearchable;
    }

    public function isHighlightEnabled(): bool
    {
        return $this->isHighlightEnabled;
    }

    public function isDisableEmptyOptionsEnabled(): bool
    {
        return $this->isDisableEmptyOptionsEnabled;
    }

    public function hasOptions(): bool
    {
        return ! empty($this->getOptions());
    }

    public function getSelectedOptionLabel(): ?string
    {
        $value = $this->getState();

        if (blank($value)) {
            return null;
        }

        $options = $this->getOptions();

        // If the value exists in current options
        if (array_key_exists($value, $options)) {
            $option = $options[$value];

            if (is_array($option)) {
                return $option['label'] ?? $option['code'].' - '.$option['title'] ?? null;
            }

            return $option;
        }

        // If option doesn't exist in current options but we want to retrieve it from DB
        if ($this->autoRetrieveSelectedOptionLabel && ($value || $value === '0' || $value === 0)) {
            $subject = Subject::find($value);

            if ($subject) {
                $code = $subject->code ?? '';
                $title = $subject->title ?? '';

                if ($code && $title) {
                    return "{$code} - {$title}";
                }

                return $subject->title;
            }
        }

        return (string) $value;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->afterStateHydrated(function (SubjectSelect $component, $state): void {
            // Nothing special needed here - the state will be set automatically
        });

        // Add a hook for dehydration to ensure we get the raw value, not the formatted one
        $this->dehydrateStateUsing(function ($state) {
            // Ensure we're storing the ID value
            if (is_string($state) && strpos($state, ' - ') !== false) {
                // If the state looks like a formatted value (e.g. "CODE - TITLE"), extract just the ID
                $code = trim(substr($state, 0, strpos($state, ' - ')));
                $subject = Subject::where('code', $code)->first();

                if ($subject) {
                    return $subject->id;
                }
            }

            return $state;
        });
    }
}
