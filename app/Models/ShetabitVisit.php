<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ShetabitVisit
 *
 * @property int $id
 * @property string|null $method
 * @property string|null $request
 * @property string|null $url
 * @property string|null $referer
 * @property string|null $languages
 * @property string|null $useragent
 * @property string|null $headers
 * @property string|null $device
 * @property string|null $platform
 * @property string|null $browser
 * @property string|null $ip
 * @property string|null $visitable_type
 * @property float|null $visitable_id
 * @property string|null $visitor_type
 * @property float|null $visitor_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class ShetabitVisit extends Model
{
    protected $table = 'shetabit_visits';

    protected $casts = [
        'visitable_id' => 'float',
        'visitor_id' => 'float',
    ];

    protected $fillable = [
        'method',
        'request',
        'url',
        'referer',
        'languages',
        'useragent',
        'headers',
        'device',
        'platform',
        'browser',
        'ip',
        'visitable_type',
        'visitable_id',
        'visitor_type',
        'visitor_id',
    ];
}
