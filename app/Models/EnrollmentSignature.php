<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class EnrollmentSignature
 *
 * @property int $id
 * @property string|null $depthead_signature
 * @property string|null $registrar_signature
 * @property string|null $cashier_signature
 * @property int|null $enrollment_id
 * @property string|null $enrollment_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class EnrollmentSignature extends Model
{
    protected $table = 'enrollment_signatures';

    protected $casts = [
        'enrollment_id' => 'int',
    ];

    protected $fillable = [
        'depthead_signature',
        'registrar_signature',
        'cashier_signature',
        'enrollment_id',
        'enrollment_type',
    ];
}
