<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class StudentTuition
 *
 * @property int $id
 * @property float|null $total_tuition
 * @property float|null $total_balance
 * @property float|null $total_lectures
 * @property float|null $total_laboratory
 * @property float|null $total_miscelaneous_fees
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $status
 * @property int|null $semester
 * @property string|null $school_year
 * @property int|null $academic_year
 * @property int|null $student_id
 * @property int|null $enrollment_id
 * @property int|null $discount
 * @property string|null $deleted_at
 * @property float|null $overall_tuition
 * @property int|null $paid
 * @property int|null $downpayment
 */
class StudentTuition extends Model
{
    use SoftDeletes;

    protected $table = 'student_tuition';

    protected $fillable = [
        'total_tuition',
        'total_balance',
        'total_lectures',
        'total_laboratory',
        'total_miscelaneous_fees',
        'status',
        'semester',
        'school_year',
        'academic_year',
        'student_id',
        'enrollment_id',
        'discount',
        'downpayment',
        'overall_tuition',
        'paid',
    ];

    protected $casts = [
        'total_tuition' => 'float',
        'total_balance' => 'float',
        'total_lectures' => 'float',
        'total_laboratory' => 'float',
        'total_miscelaneous_fees' => 'float',
        'semester' => 'integer',
        'academic_year' => 'integer',
        'student_id' => 'integer',
        'enrollment_id' => 'integer',
        'discount' => 'integer',
        'downpayment' => 'integer',
        'overall_tuition' => 'float',
        'paid' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'date',
        'deleted_at' => 'datetime',
    ];

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function studentTransactions()
    {
        return $this->student->transactions();
    }

    public function enrollment()
    {
        return $this->belongsTo(
            StudentEnrollment::class,
            'enrollment_id',
            'id'
        );
    }

    /**
     * Calculate the payment progress percentage
     *
     * @return int
     */
    public function getPaymentProgressAttribute()
    {
        if ($this->overall_tuition <= 0) {
            return 0;
        }

        $paid = $this->overall_tuition - $this->total_balance;

        return min(100, round(($paid / $this->overall_tuition) * 100));
    }

    /**
     * Get the formatted total balance
     *
     * @return string
     */
    public function getFormattedTotalBalanceAttribute()
    {
        return '₱ '.number_format($this->total_balance, 2);
    }

    /**
     * Get the formatted overall tuition
     *
     * @return string
     */
    public function getFormattedOverallTuitionAttribute()
    {
        return '₱ '.number_format($this->overall_tuition, 2);
    }

    /**
     * Get the formatted total tuition
     *
     * @return string
     */
    public function getFormattedTotalTuitionAttribute()
    {
        return '₱ '.number_format($this->total_tuition, 2);
    }

    /**
     * Get the formatted semester
     *
     * @return string
     */
    public function getFormattedSemesterAttribute()
    {
        return $this->semester.($this->semester == 1 ? 'st' : 'nd').' Semester';
    }

    /**
     * Get the payment status
     *
     * @return string
     */
    public function getPaymentStatusAttribute()
    {
        return $this->total_balance <= 0 ? 'Fully Paid' : 'Not Fully Paid';
    }

    /**
     * Get the payment status class for UI
     *
     * @return string
     */
    public function getStatusClassAttribute()
    {
        return $this->total_balance <= 0
            ? 'bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900'
            : 'bg-red-100 text-red-800 dark:bg-red-200 dark:text-red-900';
    }

    /**
     * Get the formatted total lectures
     *
     * @return string
     */
    public function getFormattedTotalLecturesAttribute()
    {
        return '₱ '.number_format($this->total_lectures, 2);
    }

    /**
     * Get the formatted total laboratory
     *
     * @return string
     */
    public function getFormattedTotalLaboratoryAttribute()
    {
        return '₱ '.number_format($this->total_laboratory, 2);
    }

    /**
     * Get the formatted total miscellaneous fees
     *
     * @return string
     */
    public function getFormattedTotalMiscelaneousFeesAttribute()
    {
        return '₱ '.number_format($this->total_miscelaneous_fees, 2);
    }

    /**
     * Get the formatted downpayment
     *
     * @return string
     */
    public function getFormattedDownpaymentAttribute()
    {
        return '₱ '.number_format($this->downpayment, 2);
    }

    /**
     * Get the formatted discount
     *
     * @return string
     */
    public function getFormattedDiscountAttribute()
    {
        return $this->discount.'%';
    }
}
