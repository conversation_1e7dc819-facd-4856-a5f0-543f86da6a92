<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ModelHasPermission
 *
 * @property float $permission_id
 * @property string $model_type
 * @property float $model_id
 */
class ModelHasPermission extends Model
{
    protected $table = 'model_has_permissions';

    public $incrementing = false;

    public $timestamps = false;

    protected $casts = [
        'permission_id' => 'float',
        'model_id' => 'float',
    ];
}
