<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogCategoryFblogPost
 *
 * @property int $id
 * @property int $post_id
 * @property int $category_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property FblogPost $fblog_post
 * @property FblogCategory $fblog_category
 */
class FblogCategoryFblogPost extends Model
{
    protected $table = 'fblog_category_fblog_post';

    protected $casts = [
        'post_id' => 'int',
        'category_id' => 'int',
    ];

    protected $fillable = [
        'post_id',
        'category_id',
    ];

    public function fblog_post()
    {
        return $this->belongsTo(FblogPost::class, 'post_id');
    }

    public function fblog_category()
    {
        return $this->belongsTo(FblogCategory::class, 'category_id');
    }
}
