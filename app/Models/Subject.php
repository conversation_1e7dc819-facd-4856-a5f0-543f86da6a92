<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Enums\SubjectEnrolledEnum;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Subject
 *
 * @property int $id
 * @property USER-DEFINED $classification
 * @property string $code
 * @property string $title
 * @property int|null $units
 * @property int|null $lecture
 * @property int|null $laboratory
 * @property string|null $pre_riquisite
 * @property int|null $academic_year
 * @property int|null $semester
 * @property int|null $course_id
 * @property string|null $group
 * @property bool $is_credited
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Subject extends Model
{
    protected $table = 'subject';

    protected $casts = [
        'classification' => SubjectEnrolledEnum::class,
        'units' => 'int',
        'lecture' => 'int',
        'laboratory' => 'int',
        'academic_year' => 'int',
        'semester' => 'int',
        'course_id' => 'int',
        'is_credited' => 'bool',
        'pre_riquisite' => 'array',
    ];

    protected $fillable = [
        'classification',
        'code',
        'title',
        'units',
        'lecture',
        'laboratory',
        'pre_riquisite',
        'academic_year',
        'semester',
        'course_id',
        'group',
        'is_credited',
    ];

    public static function boot()
    {
        parent::boot();
        // self::observe(SubjectObserver::class);

        static::creating(function ($model): void {
            $model->id = max(self::all()->pluck('id')->toArray()) + 1;
        });
    }

    public function scopeCredited($query)
    {
        return $query->where('is_credited', true);
    }

    public function isCredited(): bool
    {
        return $this->classification === SubjectEnrolledEnum::CREDITED->value;
    }

    public function isNonCredited(): bool
    {
        return $this->classification === SubjectEnrolledEnum::NON_CREDITED->value;
    }

    public function isInternal(): bool
    {
        return $this->classification === SubjectEnrolledEnum::INTERNAL->value;
    }

    public function scopeNonCredited($query)
    {
        return $query->where('is_credited', false);
    }

    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }

    public static function getSubjectsDetailsByYear($subjects, $year)
    {
        return $subjects->where('academic_year', $year)->map(function ($subject) {
            return "{$subject->title} (Code: {$subject->code}, Units: {$subject->units})";
        })->join(', ');
    }

    public function subjectEnrolleds()
    {
        return $this->hasMany(SubjectEnrollment::class, 'subject_id');
    }

    // GEt pre requisites
    public function getAllPreRequisitesAttribute()
    {
        return $this->pre_riquisite;
    }

    public static function getAvailableSubjects($selectedCourse, $academicYear, $selectedSemester, $schoolYear, $type, $selectedSubjects)
    {
        $classes = Classes::where('school_year', $schoolYear)
            ->when($type !== 'transferee', function ($query) use ($academicYear, $selectedSemester) {
                return $query->where('academic_year', $academicYear)
                    ->where('semester', $selectedSemester);
            })
            ->whereJsonContains('course_codes', (string) $selectedCourse)
            ->pluck('subject_code')
            ->toArray();

        $availableSubjects = self::query()
            ->where('course_id', $selectedCourse)
            ->whereIn('code', $classes)
            ->whereNotIn('id', $selectedSubjects);

        if ($type !== 'transferee') {
            $availableSubjects->where('academic_year', $academicYear)
                ->where('semester', $selectedSemester);
        }

        return $availableSubjects->pluck('code', 'id');
    }

    public function getLectureFeeAttribute()
    {
        return $this->lecture * $this->course->lab_per_unit;
    }

    public function getLaboratoryFeeAttribute()
    {
        return $this->laboratory * $this->course->lab_per_unit;
    }

    public function classes()
    {
        return $this->hasMany(Classes::class, 'subject_code', 'code');
    }
}
