<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BulkActionRecord
 *
 * @property int $id
 * @property int $bulk_action_id
 * @property int $record_id
 * @property string $record_type
 * @property string $status
 * @property string|null $message
 * @property Carbon|null $started_at
 * @property Carbon|null $failed_at
 * @property Carbon|null $finished_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property BulkAction $bulk_action
 */
class BulkActionRecord extends Model
{
    protected $table = 'bulk_action_records';

    protected $casts = [
        'bulk_action_id' => 'int',
        'record_id' => 'int',
        'started_at' => 'datetime',
        'failed_at' => 'datetime',
        'finished_at' => 'datetime',
    ];

    protected $fillable = [
        'bulk_action_id',
        'record_id',
        'record_type',
        'status',
        'message',
        'started_at',
        'failed_at',
        'finished_at',
    ];

    public function bulk_action()
    {
        return $this->belongsTo(BulkAction::class);
    }
}
