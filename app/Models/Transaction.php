<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Transaction
 *
 * @property int $id
 * @property string|null $transaction_number
 * @property string $description
 * @property string $status
 * @property Carbon $transaction_date
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $invoicenumber
 * @property string|null $settlements
 * @property string|null $signature
 * @property Collection|AdminTransaction[] $admin_transactions
 * @property Collection|Student[] $students
 */
class Transaction extends Model
{
    protected $table = 'transactions';

    protected $fillable = [
        'description',
        'status',
        'transaction_date',
        'transaction_number',
        'settlements',
        'invoicenumber',
        'signature',
    ];

    protected $casts = [
        'settlements' => 'array',
        'transaction_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction): void {
            $transaction->transaction_number = null; // Set to null initially
        });

        static::created(function ($transaction): void {
            $randomNumber = mt_rand(1000000000, 9999999999); // 10 digit random number
            $transaction->update(['transaction_number' => $randomNumber]);
        });

        static::deleting(function ($transaction): void {
            $transaction->studentTransactions()->delete();
            $transaction->adminTransactions()->delete();
        });
    }

    public function getTransactionTypeStringAttribute()
    {
        return ucwords(str_replace('_', ' ', $this->transaction_type));
    }

    public function getStudentFullNameAttribute()
    {
        $student = $this->student()->first();

        return $student->full_name ?? 'No Name Found';
    }

    public function getStudentCourseAttribute()
    {
        $student = $this->student()->first();

        return $student->course->code.' '.$student->academic_year;
    }

    //  get student email
    public function getStudentEmailAttribute()
    {
        $student = $this->student()->first();

        return $student->email;
    }

    //  get student personal contact
    public function getStudentPersonalContactAttribute()
    {
        $student = $this->student()->first();

        return $student->studentContactsInfo->personal_contact ?? '';
    }

    public function getStudentIdAttribute()
    {
        $student = $this->student()->first();

        return $student->id ?? 'No ID Found';
    }

    public function student()
    {
        return $this->belongsToMany(Student::class, 'student_transactions', 'transaction_id', 'student_id');
    }

    public function studentTransactions()
    {
        return $this->hasMany(StudentTransaction::class, 'transaction_id');
    }

    public function adminTransactions()
    {
        return $this->hasMany(AdminTransaction::class, 'transaction_id');
    }

    public function getTotalAmountAttribute()
    {
        $settlements = $this->settlements;

        if (is_string($settlements)) {
            $settlements = json_decode($settlements, true);
        }

        if (! is_array($settlements)) {
            return 0.00;
        }

        $total = array_reduce(array_values($settlements), function ($carry, $value) {
            return $carry + (float) $value;
        }, 0.0);

        return number_format($total, 2);
    }

    /**
     * Scope a query to sort transactions by a specific field.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $field
     * @param  string  $direction
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSort($query, $field = 'created_at', $direction = 'desc')
    {
        $allowedFields = [
            'invoicenumber',
            'description',
            'transaction_date',
            'created_at',
            'status',
            'transaction_number',
        ];

        $field = in_array($field, $allowedFields) ? $field : 'created_at';
        $direction = in_array(strtolower($direction), ['asc', 'desc']) ? $direction : 'desc';

        return $query->orderBy($field, $direction);
    }

    /**
     * Scope a query to filter transactions by date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string|null  $startDate
     * @param  string|null  $endDate
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDateRange($query, $startDate = null, $endDate = null)
    {
        if ($startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        }

        return $query;
    }

    /**
     * Scope a query to filter transactions by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $status
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStatus($query, $status = null)
    {
        if ($status) {
            return $query->where('status', $status);
        }

        return $query;
    }

    /**
     * Get the raw numeric value of the total amount.
     *
     * @return float
     */
    public function getRawTotalAmountAttribute()
    {
        $settlements = $this->settlements;

        if (is_string($settlements)) {
            $settlements = json_decode($settlements, true);
        }

        if (! is_array($settlements)) {
            return 0.00;
        }

        return array_reduce(array_values($settlements), function ($carry, $value) {
            return $carry + (float) $value;
        }, 0.0);
    }
}
