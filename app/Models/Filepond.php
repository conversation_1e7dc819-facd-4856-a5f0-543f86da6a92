<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Filepond
 *
 * @property int $id
 * @property string $filename
 * @property string $filepath
 * @property string $extension
 * @property string $mimetypes
 * @property string $disk
 * @property float|null $created_by
 * @property Carbon|null $expires_at
 * @property string|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Filepond extends Model
{
    use SoftDeletes;

    protected $table = 'fileponds';

    protected $casts = [
        'created_by' => 'float',
        'expires_at' => 'datetime',
    ];

    protected $fillable = [
        'filename',
        'filepath',
        'extension',
        'mimetypes',
        'disk',
        'created_by',
        'expires_at',
    ];
}
