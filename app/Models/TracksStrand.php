<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class TracksStrand
 *
 * @property int $id
 * @property string $code
 * @property string $title
 * @property string|null $description
 * @property int $track_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class TracksStrand extends Model
{
    protected $table = 'tracks_strands';

    protected $casts = [
        'track_id' => 'int',
    ];

    protected $fillable = [
        'code',
        'title',
        'description',
        'track_id',
    ];
}
