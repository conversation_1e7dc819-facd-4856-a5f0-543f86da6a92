<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class GuestsParentsInfo
 *
 * @property int $id
 * @property string|null $fathersname
 * @property string|null $mothersname
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class GuestsParentsInfo extends Model
{
    protected $table = 'guests_parents_info';

    protected $fillable = [
        'fathersname',
        'mothersname',
    ];
}
