<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class RoleHasPermission
 *
 * @property float $permission_id
 * @property float $role_id
 */
class RoleHasPermission extends Model
{
    protected $table = 'role_has_permissions';

    public $incrementing = false;

    public $timestamps = false;

    protected $casts = [
        'permission_id' => 'float',
        'role_id' => 'float',
    ];
}
