<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ContactsMeta
 *
 * @property int $id
 * @property float|null $model_id
 * @property string|null $model_type
 * @property float $contact_id
 * @property string $key
 * @property string|null $value
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class ContactsMeta extends Model
{
    protected $table = 'contacts_metas';

    protected $casts = [
        'model_id' => 'float',
        'contact_id' => 'float',
    ];

    protected $fillable = [
        'model_id',
        'model_type',
        'contact_id',
        'key',
        'value',
    ];
}
