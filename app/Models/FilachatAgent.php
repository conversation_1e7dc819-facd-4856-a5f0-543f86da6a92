<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FilachatAgent
 *
 * @property int $id
 * @property float $agentable_id
 * @property string $agentable_type
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class FilachatAgent extends Model
{
    protected $table = 'filachat_agents';

    protected $casts = [
        'agentable_id' => 'float',
    ];

    protected $fillable = [
        'agentable_id',
        'agentable_type',
    ];
}
