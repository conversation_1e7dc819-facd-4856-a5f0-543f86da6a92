<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PulseEntry
 *
 * @property int $id
 * @property int $timestamp
 * @property string $type
 * @property string $key
 * @property string $key_hash
 * @property int|null $value
 */
class PulseEntry extends Model
{
    protected $table = 'pulse_entries';

    public $timestamps = false;

    protected $casts = [
        'timestamp' => 'int',
        'value' => 'int',
    ];

    protected $fillable = [
        'timestamp',
        'type',
        'key',
        'key_hash',
        'value',
    ];
}
