<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PulseValue
 *
 * @property int $id
 * @property int $timestamp
 * @property string $type
 * @property string $key
 * @property string $key_hash
 * @property string $value
 */
class PulseValue extends Model
{
    protected $table = 'pulse_values';

    public $timestamps = false;

    protected $casts = [
        'timestamp' => 'int',
    ];

    protected $fillable = [
        'timestamp',
        'type',
        'key',
        'key_hash',
        'value',
    ];
}
