<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FailedImportRow
 *
 * @property int $id
 * @property string $data
 * @property float $import_id
 * @property string|null $validation_error
 * @property int|null $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User|null $user
 */
class FailedImportRow extends Model
{
    protected $table = 'failed_import_rows';

    protected $casts = [
        'import_id' => 'float',
        'user_id' => 'int',
    ];

    protected $fillable = [
        'data',
        'import_id',
        'validation_error',
        'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
