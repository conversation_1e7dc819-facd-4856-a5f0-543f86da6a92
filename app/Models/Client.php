<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Client
 *
 * @property int $id
 * @property string $name
 * @property string|null $iban
 * @property string|null $kvk_number
 * @property string|null $vat_number
 * @property string|null $address
 * @property string|null $city
 * @property string|null $zipcode
 * @property string|null $country
 * @property string|null $phone
 * @property string|null $email
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Client extends Model
{
    protected $table = 'clients';

    protected $fillable = [
        'name',
        'iban',
        'kvk_number',
        'vat_number',
        'address',
        'city',
        'zipcode',
        'country',
        'phone',
        'email',
    ];
}
