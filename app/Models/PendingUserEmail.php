<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class PendingUserEmail
 *
 * @property int $id
 * @property string $user_type
 * @property float $user_id
 * @property string $email
 * @property string $token
 * @property Carbon|null $created_at
 */
class PendingUserEmail extends Model
{
    protected $table = 'pending_user_emails';

    public $timestamps = false;

    protected $casts = [
        'user_id' => 'float',
    ];

    protected $hidden = [
        'token',
    ];

    protected $fillable = [
        'user_type',
        'user_id',
        'email',
        'token',
    ];
}
