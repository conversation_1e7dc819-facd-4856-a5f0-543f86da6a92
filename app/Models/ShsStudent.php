<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class ShsStudent
 *
 * @property int $id
 * @property string|null $student_lrn LRN (Learner Reference Number)
 * @property string|null $fullname
 * @property string|null $civil_status
 * @property string|null $religion
 * @property string|null $nationality
 * @property Carbon|null $birthdate
 * @property string|null $guardian_name
 * @property string|null $guardian_contact
 * @property string|null $student_contact
 * @property string|null $complete_address
 * @property string|null $grade_level Example: "Grade 11", "Grade 12"
 * @property string|null $gender
 * @property string|null $email
 * @property string|null $remarks
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $strand_id Foreign key for shs_strands table
 * @property int|null $track_id Foreign key for shs_tracks table
 * @property ShsStrand|null $strand The SHS strand the student is enrolled in.
 * @property ShsTrack|null $track The SHS track the student is enrolled in.
 */
class ShsStudent extends Model
{
    use HasFactory;

    protected $table = 'shs_students';

    protected $casts = [
        'strand_id' => 'int',
        'track_id' => 'int',
        'birthdate' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $fillable = [
        'student_lrn',
        'fullname',
        'civil_status',
        'religion',
        'nationality',
        'birthdate',
        'guardian_name',
        'guardian_contact',
        'student_contact',
        'complete_address',
        'grade_level',
        // 'track', // This field is deprecated in favor of track_id relationship
        'gender',
        'email',
        'remarks',
        'strand_id',
        'track_id',
    ];

    /**
     * Get the SHS strand that the student is enrolled in.
     */
    public function strand(): BelongsTo
    {
        return $this->belongsTo(ShsStrand::class, 'strand_id');
    }

    /**
     * Get the SHS track that the student is enrolled in.
     * A student must belong to a track. If they are in a strand,
     * this track_id should match the strand's track_id.
     * If the track has no strands (e.g., Sports and Arts), strand_id can be null.
     */
    public function track(): BelongsTo
    {
        return $this->belongsTo(ShsTrack::class, 'track_id');
    }
}
