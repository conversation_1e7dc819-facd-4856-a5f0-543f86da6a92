<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class TypesMeta
 *
 * @property int $id
 * @property float|null $model_id
 * @property string|null $model_type
 * @property float $type_id
 * @property string $key
 * @property string|null $value
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class TypesMeta extends Model
{
    protected $table = 'types_metas';

    protected $casts = [
        'model_id' => 'float',
        'type_id' => 'float',
    ];

    protected $fillable = [
        'model_id',
        'model_type',
        'type_id',
        'key',
        'value',
    ];
}
