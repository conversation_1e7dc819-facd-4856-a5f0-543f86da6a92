<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Contact
 *
 * @property int $id
 * @property string|null $type
 * @property string|null $status
 * @property string $name
 * @property string|null $email
 * @property string|null $phone
 * @property string $subject
 * @property string $message
 * @property bool|null $active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Contact extends Model
{
    protected $table = 'contacts';

    protected $casts = [
        'active' => 'bool',
    ];

    protected $fillable = [
        'type',
        'status',
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'active',
    ];
}
