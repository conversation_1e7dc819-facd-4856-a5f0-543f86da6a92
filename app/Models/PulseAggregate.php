<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class PulseAggregate
 *
 * @property int $id
 * @property int $bucket
 * @property int $period
 * @property string $type
 * @property string $key
 * @property string $key_hash
 * @property string $aggregate
 * @property float $value
 * @property int|null $count
 */
class PulseAggregate extends Model
{
    protected $table = 'pulse_aggregates';

    public $timestamps = false;

    protected $casts = [
        'bucket' => 'int',
        'period' => 'int',
        'value' => 'float',
        'count' => 'int',
    ];

    protected $fillable = [
        'bucket',
        'period',
        'type',
        'key',
        'key_hash',
        'aggregate',
        'value',
        'count',
    ];
}
