<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Export
 *
 * @property int $id
 * @property Carbon|null $completed_at
 * @property string $file_disk
 * @property string|null $file_name
 * @property string $exporter
 * @property int $processed_rows
 * @property int $total_rows
 * @property int $successful_rows
 * @property int|null $user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property User|null $user
 */
class Export extends Model
{
    protected $table = 'exports';

    protected $casts = [
        'completed_at' => 'datetime',
        'processed_rows' => 'int',
        'total_rows' => 'int',
        'successful_rows' => 'int',
        'user_id' => 'int',
    ];

    protected $fillable = [
        'completed_at',
        'file_disk',
        'file_name',
        'exporter',
        'processed_rows',
        'total_rows',
        'successful_rows',
        'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
