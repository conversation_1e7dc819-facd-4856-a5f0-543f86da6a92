<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class CategoryTicket
 *
 * @property float $category_id
 * @property float $ticket_id
 */
class CategoryTicket extends Model
{
    protected $table = 'category_ticket';

    public $incrementing = false;

    public $timestamps = false;

    protected $casts = [
        'category_id' => 'float',
        'ticket_id' => 'float',
    ];

    protected $fillable = [
        'category_id',
        'ticket_id',
    ];
}
