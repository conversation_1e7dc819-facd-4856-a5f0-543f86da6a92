<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Site
 *
 * @property int $id
 * @property string $url
 * @property string $name
 * @property bool $uptime_check_enabled
 * @property bool $ssl_certificate_check_enabled
 * @property int $max_request_duration_ms
 * @property Carbon|null $down_for_maintenance_at
 * @property bool $server_monitoring_notification_enabled
 * @property int|null $cpu_limit
 * @property int|null $ram_limit
 * @property int|null $disk_limit
 * @property string|null $api_token
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Site extends Model
{
    protected $table = 'sites';

    protected $casts = [
        'uptime_check_enabled' => 'bool',
        'ssl_certificate_check_enabled' => 'bool',
        'max_request_duration_ms' => 'int',
        'down_for_maintenance_at' => 'datetime',
        'server_monitoring_notification_enabled' => 'bool',
        'cpu_limit' => 'int',
        'ram_limit' => 'int',
        'disk_limit' => 'int',
    ];

    protected $hidden = [
        'api_token',
    ];

    protected $fillable = [
        'url',
        'name',
        'uptime_check_enabled',
        'ssl_certificate_check_enabled',
        'max_request_duration_ms',
        'down_for_maintenance_at',
        'server_monitoring_notification_enabled',
        'cpu_limit',
        'ram_limit',
        'disk_limit',
        'api_token',
    ];
}
