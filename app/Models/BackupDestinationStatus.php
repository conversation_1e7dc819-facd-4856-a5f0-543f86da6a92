<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BackupDestinationStatus
 *
 * @property int $id
 * @property string $name
 * @property string $disk
 * @property string $reachable
 * @property string $healthy
 * @property int $amount
 * @property string|null $newest
 * @property string $usedstorage
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class BackupDestinationStatus extends Model
{
    protected $table = 'backup_destination_statuses';

    protected $casts = [
        'amount' => 'int',
    ];

    protected $fillable = [
        'name',
        'disk',
        'reachable',
        'healthy',
        'amount',
        'newest',
        'usedstorage',
    ];
}
