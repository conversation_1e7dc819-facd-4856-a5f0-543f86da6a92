<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ExceptionLogGroup
 *
 * @property int $id
 * @property string $message
 * @property string $type
 * @property string $file
 * @property int $line
 * @property Carbon $first_seen
 * @property Carbon $last_seen
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int $site_id
 */
class ExceptionLogGroup extends Model
{
    protected $table = 'exception_log_groups';

    protected $casts = [
        'line' => 'int',
        'first_seen' => 'datetime',
        'last_seen' => 'datetime',
        'site_id' => 'int',
    ];

    protected $fillable = [
        'message',
        'type',
        'file',
        'line',
        'first_seen',
        'last_seen',
        'site_id',
    ];
}
