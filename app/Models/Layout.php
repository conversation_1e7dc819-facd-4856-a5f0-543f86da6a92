<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Layout
 *
 * @property int $id
 * @property string $user_id
 * @property string $layout_title
 * @property string $layout_slug
 * @property string $widgets
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property bool $is_active
 */
class Layout extends Model
{
    use SoftDeletes;

    protected $table = 'layouts';

    protected $casts = [
        'is_active' => 'bool',
    ];

    protected $fillable = [
        'user_id',
        'layout_title',
        'layout_slug',
        'widgets',
        'is_active',
    ];
}
