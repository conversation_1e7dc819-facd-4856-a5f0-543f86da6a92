<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Typable
 *
 * @property float $type_id
 * @property float $typables_id
 * @property string $typables_type
 */
class Typable extends Model
{
    protected $table = 'typables';

    public $incrementing = false;

    public $timestamps = false;

    protected $casts = [
        'type_id' => 'float',
        'typables_id' => 'float',
    ];

    protected $fillable = [
        'type_id',
        'typables_id',
        'typables_type',
    ];
}
