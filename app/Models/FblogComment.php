<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogComment
 *
 * @property int $id
 * @property int $user_id
 * @property int $post_id
 * @property string $comment
 * @property bool $approved
 * @property Carbon|null $approved_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property FblogPost $fblog_post
 */
class FblogComment extends Model
{
    protected $table = 'fblog_comments';

    protected $casts = [
        'user_id' => 'int',
        'post_id' => 'int',
        'approved' => 'bool',
        'approved_at' => 'datetime',
    ];

    protected $fillable = [
        'user_id',
        'post_id',
        'comment',
        'approved',
        'approved_at',
    ];

    public function fblog_post()
    {
        return $this->belongsTo(FblogPost::class, 'post_id');
    }
}
