<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Project
 *
 * @property int $id
 * @property string $name
 * @property float $hourly_rate
 * @property float $client_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Project extends Model
{
    protected $table = 'projects';

    protected $casts = [
        'hourly_rate' => 'float',
        'client_id' => 'float',
    ];

    protected $fillable = [
        'name',
        'hourly_rate',
        'client_id',
    ];
}
