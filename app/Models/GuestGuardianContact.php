<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class GuestGuardianContact
 *
 * @property int $id
 * @property string|null $emergencycontactname
 * @property int|null $emergencycontactphone
 * @property string|null $emergencycontactaddress
 */
class GuestGuardianContact extends Model
{
    protected $table = 'guest_guardian_contact';

    public $timestamps = false;

    protected $casts = [
        'emergencycontactphone' => 'int',
    ];

    protected $fillable = [
        'emergencycontactname',
        'emergencycontactphone',
        'emergencycontactaddress',
    ];
}
