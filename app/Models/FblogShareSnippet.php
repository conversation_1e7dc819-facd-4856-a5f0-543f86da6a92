<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogShareSnippet
 *
 * @property int $id
 * @property string $script_code
 * @property string $html_code
 * @property bool $active
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class FblogShareSnippet extends Model
{
    protected $table = 'fblog_share_snippets';

    protected $casts = [
        'active' => 'bool',
    ];

    protected $fillable = [
        'script_code',
        'html_code',
        'active',
    ];
}
