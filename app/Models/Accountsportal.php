<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Accountsportal
 *
 * @property int $id
 * @property string $email
 * @property Carbon|null $email_verified_at
 * @property string|null $password
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property Carbon|null $two_factor_confirmed_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $remember_token
 * @property bool|null $two_factor_auth
 * @property int|null $person_id
 * @property string|null $name
 * @property string $role
 * @property int|null $is_google_auth
 * @property string|null $avatar
 * @property string|null $cover
 * @property string|null $person_type
 */
class Accountsportal extends Model
{
    protected $table = 'accountsportal';

    protected $casts = [
        'email_verified_at' => 'datetime',
        'two_factor_confirmed_at' => 'datetime',
        'two_factor_auth' => 'bool',
        'person_id' => 'int',
        'is_google_auth' => 'int',
    ];

    protected $hidden = [
        'password',
        'two_factor_secret',
        'remember_token',
    ];

    protected $fillable = [
        'email',
        'email_verified_at',
        'password',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'remember_token',
        'two_factor_auth',
        'person_id',
        'name',
        'role',
        'is_google_auth',
        'avatar',
        'cover',
        'person_type',
    ];
}
