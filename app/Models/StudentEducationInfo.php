<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class StudentEducationInfo
 *
 * @property int $id
 * @property string|null $elementary_school
 * @property int|null $elementary_graduate_year
 * @property string|null $senior_high_name
 * @property int|null $senior_high_graduate_year
 * @property string|null $elementary_school_address
 * @property string|null $senior_high_address
 * @property string|null $junior_high_school_name
 * @property string|null $junior_high_school_address
 * @property string|null $junior_high_graduation_year
 */
class StudentEducationInfo extends Model
{
    protected $table = 'student_education_info';

    public $timestamps = false;

    protected $casts = [
        'elementary_graduate_year' => 'int',
        'senior_high_graduate_year' => 'int',
    ];

    protected $fillable = [
        'elementary_school',
        'elementary_graduate_year',
        'senior_high_name',
        'senior_high_graduate_year',
        'elementary_school_address',
        'senior_high_address',
        'junior_high_school_name',
        'junior_high_school_address',
        'junior_high_graduation_year',
    ];
}
