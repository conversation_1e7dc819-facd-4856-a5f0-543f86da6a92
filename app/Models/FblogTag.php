<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class FblogTag
 *
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Collection|FblogPost[] $fblog_posts
 */
class FblogTag extends Model
{
    protected $table = 'fblog_tags';

    protected $fillable = [
        'name',
        'slug',
    ];

    public function fblog_posts()
    {
        return $this->belongsToMany(FblogPost::class, 'fblog_post_fblog_tag', 'tag_id', 'post_id')
            ->withPivot('id')
            ->withTimestamps();
    }
}
