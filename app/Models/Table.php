<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Table
 *
 * @property int $id
 * @property string $module
 * @property string $name
 * @property string|null $comment
 * @property bool|null $timestamps
 * @property bool|null $soft_deletes
 * @property bool|null $migrated
 * @property bool|null $generated
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Table extends Model
{
    protected $table = 'tables';

    protected $casts = [
        'timestamps' => 'bool',
        'soft_deletes' => 'bool',
        'migrated' => 'bool',
        'generated' => 'bool',
    ];

    protected $fillable = [
        'module',
        'name',
        'comment',
        'timestamps',
        'soft_deletes',
        'migrated',
        'generated',
    ];
}
