<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class GuestEducationId
 *
 * @property int $id
 * @property string|null $elementaryschoolname
 * @property int|null $elementarygraduationyear
 * @property string|null $seniorhighschoolname
 * @property int|null $seniorhighgraduationyear
 * @property string|null $elementaryschooladdress
 * @property string|null $seniorhighschooladdress
 * @property string|null $juniorhighschoolname
 * @property int|null $juniorhighgraduationyear
 * @property string|null $juniorhighschooladdress
 */
class GuestEducationId extends Model
{
    protected $table = 'guest_education_id';

    public $timestamps = false;

    protected $casts = [
        'elementarygraduationyear' => 'int',
        'seniorhighgraduationyear' => 'int',
        'juniorhighgraduationyear' => 'int',
    ];

    protected $fillable = [
        'elementaryschoolname',
        'elementarygraduationyear',
        'seniorhighschoolname',
        'seniorhighgraduationyear',
        'elementaryschooladdress',
        'seniorhighschooladdress',
        'juniorhighschoolname',
        'juniorhighgraduationyear',
        'juniorhighschooladdress',
    ];
}
