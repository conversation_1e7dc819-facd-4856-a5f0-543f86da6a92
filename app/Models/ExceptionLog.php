<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ExceptionLog
 *
 * @property int $id
 * @property string $message
 * @property string $type
 * @property string $file
 * @property string $status
 * @property int $line
 * @property string $trace
 * @property string|null $request
 * @property Carbon $thrown_at
 * @property int $exception_log_group_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class ExceptionLog extends Model
{
    protected $table = 'exception_logs';

    protected $casts = [
        'line' => 'int',
        'thrown_at' => 'datetime',
        'exception_log_group_id' => 'int',
    ];

    protected $fillable = [
        'message',
        'type',
        'file',
        'status',
        'line',
        'trace',
        'request',
        'thrown_at',
        'exception_log_group_id',
    ];
}
