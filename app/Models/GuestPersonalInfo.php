<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class GuestPersonalInfo
 *
 * @property int $id
 * @property string|null $firstname
 * @property string|null $middleinitial
 * @property string|null $lastname
 * @property Carbon|null $birthdate
 * @property string|null $birthplace
 * @property string|null $citizenship
 * @property string|null $religion
 * @property string|null $sex
 * @property string|null $civilstatus
 * @property int|null $weight
 * @property int|null $height
 * @property string|null $currentaddress
 * @property string|null $permanentaddress
 * @property string|null $inputemail
 * @property int|null $phone
 * @property int|null $age
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class GuestPersonalInfo extends Model
{
    protected $table = 'guest_personal_info';

    protected $casts = [
        'birthdate' => 'datetime',
        'weight' => 'int',
        'height' => 'int',
        'phone' => 'int',
        'age' => 'int',
    ];

    protected $fillable = [
        'firstname',
        'middleinitial',
        'lastname',
        'birthdate',
        'birthplace',
        'citizenship',
        'religion',
        'sex',
        'civilstatus',
        'weight',
        'height',
        'currentaddress',
        'permanentaddress',
        'inputemail',
        'phone',
        'age',
    ];
}
