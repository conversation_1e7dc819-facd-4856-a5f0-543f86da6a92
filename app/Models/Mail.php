<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Mail
 *
 * @property int $id
 * @property string|null $uuid
 * @property string|null $mail_class
 * @property string|null $subject
 * @property string|null $from
 * @property string|null $reply_to
 * @property string|null $to
 * @property string|null $cc
 * @property string|null $bcc
 * @property string|null $html
 * @property string|null $text
 * @property int $opens
 * @property int $clicks
 * @property Carbon|null $sent_at
 * @property Carbon|null $resent_at
 * @property Carbon|null $accepted_at
 * @property Carbon|null $delivered_at
 * @property Carbon|null $last_opened_at
 * @property Carbon|null $last_clicked_at
 * @property Carbon|null $complained_at
 * @property Carbon|null $soft_bounced_at
 * @property Carbon|null $hard_bounced_at
 * @property Carbon|null $unsubscribed_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Collection|MailEvent[] $mail_events
 * @property Collection|Mailable[] $mailables
 * @property Collection|MailAttachment[] $mail_attachments
 */
class Mail extends Model
{
    protected $table = 'mails';

    protected $casts = [
        'opens' => 'int',
        'clicks' => 'int',
        'sent_at' => 'datetime',
        'resent_at' => 'datetime',
        'accepted_at' => 'datetime',
        'delivered_at' => 'datetime',
        'last_opened_at' => 'datetime',
        'last_clicked_at' => 'datetime',
        'complained_at' => 'datetime',
        'soft_bounced_at' => 'datetime',
        'hard_bounced_at' => 'datetime',
        'unsubscribed_at' => 'datetime',
    ];

    protected $fillable = [
        'uuid',
        'mail_class',
        'subject',
        'from',
        'reply_to',
        'to',
        'cc',
        'bcc',
        'html',
        'text',
        'opens',
        'clicks',
        'sent_at',
        'resent_at',
        'accepted_at',
        'delivered_at',
        'last_opened_at',
        'last_clicked_at',
        'complained_at',
        'soft_bounced_at',
        'hard_bounced_at',
        'unsubscribed_at',
    ];

    public function mail_events()
    {
        return $this->hasMany(MailEvent::class);
    }

    public function mailables()
    {
        return $this->hasMany(Mailable::class);
    }

    public function mail_attachments()
    {
        return $this->hasMany(MailAttachment::class);
    }
}
