<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class ModelHasRole
 *
 * @property float $role_id
 * @property string $model_type
 * @property float $model_id
 */
class ModelHasRole extends Model
{
    protected $table = 'model_has_roles';

    public $incrementing = false;

    public $timestamps = false;

    protected $casts = [
        'role_id' => 'float',
        'model_id' => 'float',
    ];
}
