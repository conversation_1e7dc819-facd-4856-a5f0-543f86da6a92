<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class StudentsPersonalInfo
 *
 * @property int $id
 * @property string|null $birthplace
 * @property string|null $civil_status
 * @property string|null $citizenship
 * @property string|null $religion
 * @property string|null $weight
 * @property string|null $height
 * @property string|null $current_adress
 * @property string|null $permanent_address
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class StudentsPersonalInfo extends Model
{
    protected $table = 'students_personal_info';

    protected $fillable = [
        'birthplace',
        'civil_status',
        'citizenship',
        'religion',
        'weight',
        'height',
        'current_adress',
        'permanent_address',
    ];
}
