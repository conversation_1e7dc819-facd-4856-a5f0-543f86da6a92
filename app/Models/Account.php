<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Account
 *
 * @property int $id
 * @property string|null $type
 * @property float|null $parent_id
 * @property string|null $name
 * @property string $username
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $loginby
 * @property string|null $address
 * @property string|null $lang
 * @property string|null $password
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property Carbon|null $two_factor_confirmed_at
 * @property string|null $otp_code
 * @property Carbon|null $otp_activated_at
 * @property Carbon|null $last_login
 * @property string|null $agent
 * @property string|null $host
 * @property bool|null $is_login
 * @property bool $is_active
 * @property bool $is_notification_active
 * @property string|null $deleted_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property int|null $person_id
 * @property string|null $profile_photo_url
 * @property string|null $email_verified_at
 * @property string|null $role
 * @property string|null $avatar
 * @property string|null $person_type
 * @property string|null $profile_photo_path
 * @property string|null $remember_token
 */
class Account extends Model
{
    use SoftDeletes;

    protected $table = 'accounts';

    protected $casts = [
        'parent_id' => 'float',
        'two_factor_confirmed_at' => 'datetime',
        'otp_activated_at' => 'datetime',
        'last_login' => 'datetime',
        'is_login' => 'bool',
        'is_active' => 'bool',
        'is_notification_active' => 'bool',
        'person_id' => 'int',
    ];

    protected $hidden = [
        'password',
        'two_factor_secret',
        'remember_token',
    ];

    protected $fillable = [
        'type',
        'parent_id',
        'name',
        'username',
        'email',
        'phone',
        'loginby',
        'address',
        'lang',
        'password',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'otp_code',
        'otp_activated_at',
        'last_login',
        'agent',
        'host',
        'is_login',
        'is_active',
        'is_notification_active',
        'person_id',
        'profile_photo_url',
        'email_verified_at',
        'role',
        'avatar',
        'person_type',
        'profile_photo_path',
        'remember_token',
    ];
}
