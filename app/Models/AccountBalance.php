<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountBalance
 *
 * @property int $id
 * @property int $student_id
 * @property string $semester
 * @property string $academic_year
 * @property string $school_year
 * @property float $total_fees
 * @property float $amount_paid
 * @property float $balance
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class AccountBalance extends Model
{
    protected $table = 'account_balance';

    protected $casts = [
        'student_id' => 'int',
        'total_fees' => 'float',
        'amount_paid' => 'float',
        'balance' => 'float',
    ];

    protected $fillable = [
        'student_id',
        'semester',
        'academic_year',
        'school_year',
        'total_fees',
        'amount_paid',
        'balance',
    ];
}
