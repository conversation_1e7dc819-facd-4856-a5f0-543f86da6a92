<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class ConnectedAccount
 *
 * @property int $id
 * @property float $user_id
 * @property string $provider
 * @property string $provider_id
 * @property string|null $name
 * @property string|null $nickname
 * @property string|null $email
 * @property string|null $telephone
 * @property string|null $avatar_path
 * @property string $token
 * @property string|null $secret
 * @property string|null $refresh_token
 * @property Carbon|null $expires_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class ConnectedAccount extends Model
{
    protected $table = 'connected_accounts';

    protected $casts = [
        'user_id' => 'float',
        'expires_at' => 'datetime',
    ];

    protected $hidden = [
        'token',
        'secret',
        'refresh_token',
    ];

    protected $fillable = [
        'user_id',
        'provider',
        'provider_id',
        'name',
        'nickname',
        'email',
        'telephone',
        'avatar_path',
        'token',
        'secret',
        'refresh_token',
        'expires_at',
    ];
}
