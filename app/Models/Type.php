<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Type
 *
 * @property int $id
 * @property int $order
 * @property float|null $parent_id
 * @property string|null $model_type
 * @property float|null $model_id
 * @property string|null $for
 * @property string|null $type
 * @property string $name
 * @property string $key
 * @property string|null $description
 * @property string|null $color
 * @property string|null $icon
 * @property bool|null $is_activated
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Type extends Model
{
    protected $table = 'types';

    protected $casts = [
        'order' => 'int',
        'parent_id' => 'float',
        'model_id' => 'float',
        'is_activated' => 'bool',
    ];

    protected $fillable = [
        'order',
        'parent_id',
        'model_type',
        'model_id',
        'for',
        'type',
        'name',
        'key',
        'description',
        'color',
        'icon',
        'is_activated',
    ];
}
