<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class WebauthnKey
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $name
 * @property string $credential_id
 * @property string $public_key
 * @property string|null $attachment_type
 * @property bool $is_passkey
 * @property string|null $transports
 * @property Carbon|null $last_used_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class WebauthnKey extends Model
{
    protected $table = 'webauthn_keys';

    protected $casts = [
        'user_id' => 'int',
        'is_passkey' => 'bool',
        'last_used_at' => 'datetime',
    ];

    protected $fillable = [
        'user_id',
        'name',
        'credential_id',
        'public_key',
        'attachment_type',
        'is_passkey',
        'transports',
        'last_used_at',
    ];
}
