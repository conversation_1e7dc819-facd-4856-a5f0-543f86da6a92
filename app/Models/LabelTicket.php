<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class LabelTicket
 *
 * @property float $label_id
 * @property float $ticket_id
 */
class LabelTicket extends Model
{
    protected $table = 'label_ticket';

    public $incrementing = false;

    public $timestamps = false;

    protected $casts = [
        'label_id' => 'float',
        'ticket_id' => 'float',
    ];

    protected $fillable = [
        'label_id',
        'ticket_id',
    ];
}
