<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Timesheet
 *
 * @property int $id
 * @property Carbon $date
 * @property float $hours
 * @property string|null $description
 * @property float $project_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 */
class Timesheet extends Model
{
    protected $table = 'timesheets';

    protected $casts = [
        'date' => 'datetime',
        'hours' => 'float',
        'project_id' => 'float',
    ];

    protected $fillable = [
        'date',
        'hours',
        'description',
        'project_id',
    ];
}
