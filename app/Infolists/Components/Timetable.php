<?php

namespace App\Infolists\Components;

use Filament\Infolists\Components\Entry;

class Timetable extends Entry
{
    protected string $view = 'infolists.components.timetable';

    public function getSchedule(): array
    {
        return $this->getRecord()->Schedule;
    }

    public function getDayOfWeek(): string
    {
        return $this->getRecord()->day_of_week;
    }

    public function getStartTime(): string
    {
        return $this->getRecord()->start_time;
    }

    public function getEndTime(): string
    {
        return $this->getRecord()->end_time;
    }

    public function getRoom(): string
    {
        return $this->getRecord()->room;
    }

    public function getCode(): string
    {
        return $this->getRecord()->code;
    }
}
