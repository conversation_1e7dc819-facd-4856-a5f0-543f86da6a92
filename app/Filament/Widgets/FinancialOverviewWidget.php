<?php

namespace App\Filament\Widgets;

use App\Models\GeneralSetting;
use App\Models\StudentTuition;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class FinancialOverviewWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $settings = GeneralSetting::first();
        $currentSchoolYear = $settings->getSchoolYearString();
        $currentSemester = $settings->semester;

        $tuitionData = StudentTuition::whereHas('enrollment', function (
            $query
        ) use ($currentSchoolYear, $currentSemester): void {
            $query
                ->where('school_year', $currentSchoolYear)
                ->where('semester', $currentSemester);
        })
            ->select(
                DB::raw('SUM(overall_tuition) as total_fees'),
                DB::raw(
                    'SUM(CASE WHEN paid = 1 THEN overall_tuition ELSE 0 END) as total_collected'
                ),
                DB::raw('COUNT(*) as total_records')
            )
            ->first();

        $totalFees = $tuitionData->total_fees ?? 0;
        $totalCollected = $tuitionData->total_collected ?? 0;
        $collectionRate =
            $totalFees > 0 ? ($totalCollected / $totalFees) * 100 : 0;
        $outstandingAmount = $totalFees - $totalCollected;

        // Get count of fully paid tuitions
        $fullyPaidCount = StudentTuition::whereHas('enrollment', function (
            $query
        ) use ($currentSchoolYear, $currentSemester): void {
            $query
                ->where('school_year', $currentSchoolYear)
                ->where('semester', $currentSemester);
        })
            ->where('paid', 1)
            ->count();

        $totalStudents = $tuitionData->total_records ?? 0;
        $paymentCompletionRate =
            $totalStudents > 0 ? ($fullyPaidCount / $totalStudents) * 100 : 0;

        return [
            Stat::make(
                'Total Collections',
                '₱'.number_format($totalCollected, 2)
            )
                ->description(
                    number_format($collectionRate, 1).'% collection rate'
                )
                ->descriptionIcon('heroicon-m-banknotes')
                ->chart([7, 3, 4, 5, 6, $collectionRate])
                ->color('success'),

            Stat::make(
                'Outstanding Balance',
                '₱'.number_format($outstandingAmount, 2)
            )
                ->description(
                    'From total fees of ₱'.number_format($totalFees, 2)
                )
                ->descriptionIcon('heroicon-m-calculator')
                ->chart([7, 3, 4, 5, 6, $outstandingAmount])
                ->color('danger'),

            Stat::make(
                'Payment Completion',
                number_format($paymentCompletionRate, 1).'%'
            )
                ->description(
                    "{$fullyPaidCount} out of {$totalStudents} students fully paid"
                )
                ->descriptionIcon('heroicon-m-check-badge')
                ->chart([7, 3, 4, 5, 6, $paymentCompletionRate])
                ->color('info'),
        ];
    }

    public static function canView(): bool
    {
        return auth()->user()->can('widget_FinancialOverviewWidget');
    }
}
