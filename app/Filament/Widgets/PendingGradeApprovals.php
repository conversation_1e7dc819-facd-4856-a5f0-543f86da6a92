<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\GradeApprovalResource;
use App\Models\ClassEnrollment;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class PendingGradeApprovals extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        $pendingCount = ClassEnrollment::query()
            ->where('is_grades_finalized', true)
            ->where('is_grades_verified', false)
            ->count();

        $verifiedCount = ClassEnrollment::query()
            ->where('is_grades_verified', true)
            ->count();

        $totalClassesWithPending = ClassEnrollment::query()
            ->where('is_grades_finalized', true)
            ->where('is_grades_verified', false)
            ->distinct('class_id')
            ->count('class_id');

        return [
            Stat::make('Pending Grade Approvals', $pendingCount)
                ->description('Waiting for verification')
                ->descriptionIcon('heroicon-m-clock')
                ->color($pendingCount > 0 ? 'warning' : 'gray')
                ->url(
                    GradeApprovalResource::getUrl('index', [
                        'tableFilters' => [
                            'verification_status' => [
                                'value' => 'pending',
                            ],
                        ],
                    ])
                ),

            Stat::make('Classes with Pending Grades', $totalClassesWithPending)
                ->description('Classes needing approval')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color($totalClassesWithPending > 0 ? 'danger' : 'gray'),

            Stat::make('Verified Grade Entries', $verifiedCount)
                ->description('Approved by admin')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
        ];
    }

    public static function canView(): bool
    {
        return auth()->user()->can('widget_PendingGradeApprovals');
    }
}
