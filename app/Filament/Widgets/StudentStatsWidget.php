<?php

namespace App\Filament\Widgets;

use App\Models\Student;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StudentStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $totalStudents = Student::count();
        $maleStudents = Student::where('gender', 'Male')->count();
        $femaleStudents = Student::where('gender', 'Female')->count();
        $activeStudents = Student::where('status', 'Active')->count();

        return [
            Stat::make('Total Students', $totalStudents)
                ->description('Total number of students in the system')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->chart([7, 3, 4, 5, 6, $totalStudents])
                ->color('primary'),

            Stat::make(
                'Gender Distribution',
                $maleStudents.' / '.$femaleStudents
            )
                ->description('Male / Female Students')
                ->descriptionIcon('heroicon-m-users')
                ->chart([$maleStudents, $femaleStudents])
                ->color('success'),

            Stat::make('Active Students', $activeStudents)
                ->description(
                    number_format(($activeStudents / $totalStudents) * 100, 1).
                        '% of total students'
                )
                ->descriptionIcon('heroicon-m-user-group')
                ->chart([7, 3, 4, 5, 6, $activeStudents])
                ->color('info'),
        ];
    }

    public static function canView(): bool
    {
        return auth()->user()->can('widget_StudentStatsWidget');
    }
}
