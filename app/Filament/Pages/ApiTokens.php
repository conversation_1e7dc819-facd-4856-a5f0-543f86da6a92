<?php

namespace App\Filament\Pages;

use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;
use Rawilk\ProfileFilament\Concerns\HasPanelPageRoutes;
use Rawilk\ProfileFilament\Filament\Clusters\Profile;

class ApiTokens extends Page
{
    use HasPanelPageRoutes;

    protected static ?string $cluster = Profile::class;

    protected static ?string $navigationIcon = 'heroicon-o-key'; // Changed icon

    protected static string $view = 'filament.pages.api-tokens';

    protected static ?string $slug = 'api-tokens';

    protected static ?int $navigationSort = 10;

    public ?string $newToken = null; // Property to hold the newly generated token

    public $tokens; // Property to hold existing tokens

    // Mount method to load existing tokens
    public function mount(): void
    {
        $this->loadTokens();
    }

    protected function loadTokens(): void
    {
        $this->tokens = Auth::user()->tokens()->orderBy('created_at', 'desc')->get();
    }

    // Action to generate a new token
    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateToken')
                ->label('Generate New Token')
                ->icon('heroicon-o-plus-circle')
                ->form([
                    TextInput::make('token_name')
                        ->label('Token Name')
                        ->required()
                        ->maxLength(255),
                ])
                ->action(function (array $data): void {
                    $user = Auth::user();
                    $tokenName = $data['token_name'];

                    // Optional: Add abilities/scopes if needed
                    // $newToken = $user->createToken($tokenName, ['scope1', 'scope2']);
                    $newToken = $user->createToken($tokenName);

                    // Store the plain text token temporarily to display it once
                    $this->newToken = $newToken->plainTextToken;

                    // Reload the token list
                    $this->loadTokens();

                    Notification::make()
                        ->title('Token Generated Successfully')
                        ->success()
                        ->send();
                }),
        ];
    }

    // Action to delete a token
    public function deleteToken(int $tokenId): void
    {
        $token = PersonalAccessToken::find($tokenId);

        // Ensure the token belongs to the current user before deleting
        if ($token && $token->tokenable_id === Auth::id()) {
            $token->delete();
            $this->loadTokens(); // Refresh the list
            Notification::make()
                ->title('Token Deleted')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Error Deleting Token')
                ->body('Token not found or you do not have permission to delete it.')
                ->danger()
                ->send();
        }
    }

    public static function getNavigationLabel(): string
    {
        return __('API Tokens');
    }

    public function getTitle(): string|Htmlable
    {
        return __('API Tokens');
    }

    public function getHeading(): string|Htmlable
    {
        return __('API Tokens');
    }
}
