<?php

namespace App\Filament\Faculty\Resources\StudentResource\Pages;

use App\Filament\Faculty\Resources\StudentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListStudents extends ListRecords
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back_to_classes')
                ->label('Back to Classes')
                ->url(route('filament.faculty.resources.classes.index'))
                ->icon('heroicon-o-arrow-left'),
        ];
    }
}
