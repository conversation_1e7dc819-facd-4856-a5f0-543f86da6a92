<?php

namespace App\Filament\Faculty\Resources\StudentResource\Pages;

use App\Filament\Faculty\Resources\StudentResource;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewStudent extends ViewRecord
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('retryClassEnrollment')
                ->label('Retry Class Enrollment')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Retry Class Enrollment?')
                ->modalDescription('This will attempt to re-enroll the student in all available classes for their current subjects.')
                ->form([
                    Toggle::make('force_enrollment')
                        ->label('Force Enrollment')
                        ->helperText('Override maximum class size limits when enrolling')
                        ->default(true),
                    Select::make('enrollment_id')
                        ->label('Enrollment to Use')
                        ->options(function ($record) {
                            return $record->subjectEnrolled()
                                ->select('enrollment_id')
                                ->distinct()
                                ->get()
                                ->pluck('enrollment_id', 'enrollment_id')
                                ->map(function ($id) {
                                    return "Enrollment #{$id}";
                                })
                                ->toArray();
                        })
                        ->helperText('Select which enrollment to use for class assignments. Leave empty to use all subjects.')
                        ->searchable()
                        ->placeholder('All Subjects'),
                ])
                ->action(function (array $data, $record): void {
                    // Temporarily override the force_enroll_when_full config if needed
                    $originalConfigValue = config('enrollment.force_enroll_when_full');
                    if ($data['force_enrollment']) {
                        config(['enrollment.force_enroll_when_full' => true]);
                    }

                    try {
                        // Attempt to auto-enroll using the specified enrollment ID or null for all subjects
                        $enrollmentId = $data['enrollment_id'] ?? null;
                        $record->autoEnrollInClasses($enrollmentId);

                        Notification::make()
                            ->success()
                            ->title('Enrollment Retry Complete')
                            ->body('The system has attempted to enroll the student in all classes. Check the notification for results.')
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Enrollment Retry Failed')
                            ->body('An error occurred: '.$e->getMessage())
                            ->send();
                    } finally {
                        // Restore original config value
                        if ($data['force_enrollment']) {
                            config(['enrollment.force_enroll_when_full' => $originalConfigValue]);
                        }
                    }
                }),
            \Filament\Actions\Action::make('back')
                ->label('Back to Roster')
                ->url($this->getResource()::getUrl('index', ['class_id' => request('class_id')]))
                ->icon('heroicon-o-arrow-left'),
        ];
    }
}
