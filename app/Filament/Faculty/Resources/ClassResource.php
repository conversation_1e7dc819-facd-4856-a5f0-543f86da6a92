<?php

namespace App\Filament\Faculty\Resources;

use App\Filament\Faculty\Resources\ClassResource\Pages;
use App\Models\Classes;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ClassResource extends Resource
{
    protected static ?string $model = Classes::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?string $navigationLabel = 'My Classes';

    protected static ?int $navigationSort = 1;

    protected static bool $shouldSkipAuthorization = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Class Information')
                    ->schema([
                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('subject.code')
                                ->label('Subject Code')
                                ->disabled(),
                            Forms\Components\TextInput::make('subject.title')
                                ->label('Subject Title')
                                ->disabled(),
                            Forms\Components\TextInput::make('section')
                                ->disabled(),
                        ])->columns(3),

                        Forms\Components\Group::make([
                            Forms\Components\TextInput::make('academic_year')
                                ->disabled(),
                            Forms\Components\TextInput::make('semester')
                                ->disabled(),
                            Forms\Components\TextInput::make('maximum_slots')
                                ->disabled(),
                        ])->columns(3),
                    ]),

                Forms\Components\Section::make('Schedule & Location')
                    ->schema([
                        Forms\Components\Placeholder::make('schedule')
                            ->label('Class Schedule')
                            ->content(fn (Classes $record) => $record->schedule?->map(fn ($s) => "{$s->day} {$s->start_time}-{$s->end_time}"
                            )->join(', ') ?? 'No schedule set'),

                        Forms\Components\Placeholder::make('room')
                            ->label('Assigned Room')
                            ->content(fn (Classes $record) => $record->room?->name ?? 'No room assigned'),
                    ])->columns(2),

                Forms\Components\Section::make('Class Statistics')
                    ->schema([
                        Forms\Components\Placeholder::make('enrolled_count')
                            ->label('Enrolled Students')
                            ->content(fn (Classes $record) => $record->enrollments()->count().' / '.$record->maximum_slots),

                        Forms\Components\Placeholder::make('grade_status')
                            ->label('Grade Status')
                            ->content(fn (Classes $record) => static::getGradeStatusSummary($record)),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->contentGrid([
                'md' => 2,
                'lg' => 3,
            ])
            ->columns([
                Split::make([
                    Stack::make([
                        TextColumn::make('subject.code')
                            ->label('Subject')
                            ->description(fn (Classes $record) => $record->subject->title)
                            ->searchable()
                            ->sortable()
                            ->weight(FontWeight::Bold)
                            ->size(TextColumn\TextColumnSize::Large),

                        TextColumn::make('section')
                            ->badge()
                            ->color('primary')
                            ->icon('heroicon-m-academic-cap'),

                        TextColumn::make('schedule_summary')
                            ->label('Schedule')
                            ->description(fn (Classes $record) => $record->room?->name)
                            ->formatStateUsing(fn (Classes $record) => $record->schedule?->map(fn ($s) => "{$s->day} {$s->start_time}-{$s->end_time}"
                            )->join(', ')),
                    ]),

                    Panel::make([
                        Stack::make([
                            TextColumn::make('enrollments_count')
                                ->label('Enrolled')
                                ->counts('enrollments')
                                ->description('students')
                                ->color('success')
                                ->icon('heroicon-m-users')
                                ->alignRight(),

                            TextColumn::make('pending_grades_count')
                                ->label('Pending Grades')
                                ->counts('enrollments', fn ($query) => $query->where('is_grades_finalized', false))
                                ->color('warning')
                                ->icon('heroicon-m-clipboard-document-list')
                                ->alignRight(),
                        ])->space(1),
                    ]),
                ]),
            ])
            ->filters([
                SelectFilter::make('semester')
                    ->options([
                        '1' => 'First Semester',
                        '2' => 'Second Semester',
                    ])
                    ->label('Semester'),

                SelectFilter::make('academic_year')
                    ->options(fn () => Classes::distinct('academic_year')
                        ->pluck('academic_year', 'academic_year')
                        ->toArray())
                    ->label('Academic Year'),
            ])
            ->actions([
                ActionGroup::make([
                    Action::make('view')
                        ->label('View Details')
                        ->icon('heroicon-m-information-circle')
                        ->url(fn (Classes $record): string => static::getUrl('view', ['record' => $record])),

                    Action::make('manage_grades')
                        ->label('Gradebook')
                        ->icon('heroicon-m-clipboard-document-list')
                        ->color('warning')
                        ->url(fn (Classes $record): string => ClassGradeManagementResource::getUrl('index', ['class_id' => $record->id])),

                    Action::make('view_students')
                        ->label('Student Roster')
                        ->icon('heroicon-m-users')
                        ->url(fn (Classes $record): string => StudentResource::getUrl('index', ['class_id' => $record->id])),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->defaultPaginationPageOption(12)
            ->deferLoading();
    }

    protected static function getGradeStatusSummary(Classes $class): string
    {
        $total = $class->enrollments()->count();
        $finalized = $class->enrollments()->where('is_grades_finalized', true)->count();
        $verified = $class->enrollments()->where('is_grades_verified', true)->count();

        return "Total: {$total} | Finalized: {$finalized} | Verified: {$verified}";
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClasses::route('/'),
            'view' => Pages\ViewClass::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('faculty_id', auth()->id())
            ->withCount(['enrollments', 'enrollments as pending_grades_count' => function ($query): void {
                $query->where('is_grades_finalized', false);
            }]);
    }
}
