<?php

namespace App\Filament\Faculty\Resources;

use App\Filament\Faculty\Resources\ClassGradeManagementResource\Pages;
use App\Models\ClassEnrollment;
use Filament\Forms;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight as EnumsFontWeight;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class ClassGradeManagementResource extends Resource
{
    protected static ?string $model = ClassEnrollment::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?string $navigationLabel = 'Grade Management';

    protected static ?int $navigationSort = 2;

    protected static ?string $pluralModelLabel = 'Class Grade Submitions';

    protected static bool $shouldSkipAuthorization = true;

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_grades_finalized', false)
            ->whereHas('class', function ($query): void {
                $query->where('faculty_id', Auth::id());
            })
            ->count();
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make('Grade Components')->schema([
                Grid::make(3)->schema([
                    TextInput::make('prelim_grade')
                        ->numeric()
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->suffix('%')
                        ->reactive()
                        ->afterStateUpdated(
                            fn (
                                Get $get,
                                Set $set
                            ) => self::calculateTotalAverage($get, $set)
                        )
                        ->extraInputAttributes([
                            'class' => 'text-center text-lg',
                        ])
                        ->disabled(fn ($record) => $record->is_grades_verified),

                    TextInput::make('midterm_grade')
                        ->numeric()
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->suffix('%')
                        ->reactive()
                        ->afterStateUpdated(
                            fn (
                                Get $get,
                                Set $set
                            ) => self::calculateTotalAverage($get, $set)
                        )
                        ->extraInputAttributes([
                            'class' => 'text-center text-lg',
                        ])
                        ->disabled(fn ($record) => $record->is_grades_verified),

                    TextInput::make('finals_grade')
                        ->numeric()
                        ->minValue(0)
                        ->maxValue(100)
                        ->step(0.01)
                        ->suffix('%')
                        ->reactive()
                        ->afterStateUpdated(
                            fn (
                                Get $get,
                                Set $set
                            ) => self::calculateTotalAverage($get, $set)
                        )
                        ->extraInputAttributes([
                            'class' => 'text-center text-lg',
                        ])
                        ->disabled(fn ($record) => $record->is_grades_verified),
                ]),

                Grid::make(2)->schema([
                    TextInput::make('total_average')
                        ->numeric()
                        ->disabled()
                        ->suffix('%')
                        ->extraInputAttributes(
                            fn ($get) => [
                                'class' => 'font-bold text-lg '.
                                    ($get('total_average') >= 75
                                        ? 'text-success-600'
                                        : 'text-danger-600'),
                            ]
                        ),

                    Select::make('remarks')
                        ->options([
                            'PASSED' => 'Passed',
                            'FAILED' => 'Failed',
                        ])
                        ->disabled()
                        ->placeholder('Auto-calculated'),
                ]),
            ]),

            // Add a verification status section
            Section::make('Verification Status')
                ->schema([
                    Grid::make(2)->schema([
                        Forms\Components\Placeholder::make('status')
                            ->label('Status')
                            ->content(
                                fn ($record) => $record->is_grades_verified
                                    ? 'Verified by Admin (Locked)'
                                    : ($record->is_grades_finalized
                                        ? 'Finalized, Pending Admin Verification'
                                        : 'Draft')
                            ),

                        Forms\Components\Placeholder::make('verification_time')
                            ->label('Verification Time')
                            ->content(
                                fn ($record) => $record->verified_at
                                    ? $record->verified_at->format(
                                        'F j, Y, g:i a'
                                    )
                                    : 'Not verified yet'
                            )
                            ->visible(
                                fn ($record) => $record->is_grades_verified
                            ),
                    ]),
                ])
                ->visible(
                    fn ($record) => $record->is_grades_finalized ||
                        $record->is_grades_verified
                ),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('student.full_name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => $record->student->student_id),

                TextColumn::make('prelim_grade')
                    ->numeric()
                    ->default(0)
                    ->color(fn ($state) => $state >= 75 ? 'success' : 'danger')
                    ->icon(
                        fn ($state) => $state >= 75
                            ? 'heroicon-o-check-circle'
                            : 'heroicon-o-x-circle'
                    ),

                TextColumn::make('midterm_grade')
                    ->numeric()
                    ->default(0)
                    ->color(fn ($state) => $state >= 75 ? 'success' : 'danger'),

                TextColumn::make('finals_grade')
                    ->numeric()
                    ->default(0)
                    ->color(fn ($state) => $state >= 75 ? 'success' : 'danger'),

                TextColumn::make('total_average')
                    ->numeric()
                    ->default(0)
                    ->color(fn ($state) => $state >= 75 ? 'success' : 'danger')
                    ->weight(EnumsFontWeight::Bold),

                IconColumn::make('status')
                    ->label('Status')
                    ->icons([
                        'heroicon-o-lock-closed' => fn (
                            $record
                        ) => $record->is_grades_verified,
                        'heroicon-o-document-check' => fn (
                            $record
                        ) => $record->is_grades_finalized &&
                            ! $record->is_grades_verified,
                        'heroicon-o-pencil' => fn (
                            $record
                        ) => ! $record->is_grades_finalized,
                    ])
                    ->colors([
                        'success' => fn ($record) => $record->is_grades_verified,
                        'primary' => fn (
                            $record
                        ) => $record->is_grades_finalized &&
                            ! $record->is_grades_verified,
                        'gray' => fn ($record) => ! $record->is_grades_finalized,
                    ])
                    ->tooltip(
                        fn ($record) => $record->is_grades_verified
                            ? 'Verified by Admin (Locked)'
                            : ($record->is_grades_finalized
                                ? 'Finalized, Pending Admin Verification'
                                : 'Draft')
                    ),
            ])
            ->filters([
                TernaryFilter::make('verification_status')
                    ->placeholder('All')
                    ->trueLabel('Verified')
                    ->falseLabel('Unverified')
                    ->queries(
                        true: fn ($query) => $query->where(
                            'is_grades_verified',
                            true
                        ),
                        false: fn ($query) => $query->where(
                            'is_grades_verified',
                            false
                        )
                    ),

                TernaryFilter::make('finalization_status')
                    ->placeholder('All')
                    ->trueLabel('Finalized')
                    ->falseLabel('Draft')
                    ->queries(
                        true: fn ($query) => $query->where(
                            'is_grades_finalized',
                            true
                        ),
                        false: fn ($query) => $query->where(
                            'is_grades_finalized',
                            false
                        )
                    ),
            ])
            ->actions([
                Tables\Actions\EditAction::make()->visible(
                    fn (Model $record) => ! $record->is_grades_verified
                ),

                Action::make('finalize')
                    ->label('Finalize Grades')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(
                        fn (Model $record) => ! $record->is_grades_finalized &&
                            ! $record->is_grades_verified
                    )
                    ->action(function (Model $record): void {
                        $record->update([
                            'is_grades_finalized' => true,
                        ]);
                    }),

                Action::make('unfinalize')
                    ->label('Revert to Draft')
                    ->icon('heroicon-o-arrow-path')
                    ->color('gray')
                    ->requiresConfirmation()
                    ->visible(
                        fn (Model $record) => $record->is_grades_finalized &&
                            ! $record->is_grades_verified
                    )
                    ->action(function (Model $record): void {
                        $record->update([
                            'is_grades_finalized' => false,
                        ]);
                    }),

                Action::make('view_details')
                    ->label('View Details')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn (Model $record) => $record->is_grades_verified)
                    ->modalHeading(
                        fn (
                            Model $record
                        ) => "Grade Details for {$record->student->full_name}"
                    )
                    ->modalContent(
                        fn (Model $record) => view(
                            'filament.faculty.components.grade-details',
                            [
                                'record' => $record,
                            ]
                        )
                    ),
            ])
            ->bulkActions([
                BulkAction::make('finalize')
                    ->label('Finalize Selected')
                    ->icon('heroicon-o-check-circle')
                    ->requiresConfirmation()
                    ->action(function (Collection $records): void {
                        $records->each(function ($record): void {
                            if (
                                ! $record->is_grades_verified &&
                                ! $record->is_grades_finalized
                            ) {
                                $record->update([
                                    'is_grades_finalized' => true,
                                ]);
                            }
                        });
                    }),
            ]);
    }

    public static function calculateTotalAverage(Get $get, callable $set): void
    {
        $prelim = floatval($get('prelim_grade') ?? 0);
        $midterm = floatval($get('midterm_grade') ?? 0);
        $finals = floatval($get('finals_grade') ?? 0);

        $average = ($prelim + $midterm + $finals) / 3;
        $set('total_average', round($average, 2));

        // Set remarks based on the average
        $remarks = match (true) {
            $average >= 75 => 'PASSED',
            default => 'FAILED',
        };
        $set('remarks', $remarks);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGrades::route('/'),
            'edit' => Pages\EditGrades::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()->whereHas('class', function (
            $query
        ): void {
            $query->where('faculty_id', Auth::id());

            // Add class_id filter if present in request
            if (request()->has('class_id')) {
                $query->where('id', request('class_id'));
            }
        });

        return $query;
    }
}
