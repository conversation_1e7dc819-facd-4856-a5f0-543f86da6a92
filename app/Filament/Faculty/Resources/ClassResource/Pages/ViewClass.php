<?php

namespace App\Filament\Faculty\Resources\ClassResource\Pages;

use App\Filament\Faculty\Resources\ClassGradeManagementResource;
use App\Filament\Faculty\Resources\ClassResource;
use App\Filament\Faculty\Resources\StudentResource;
use Filament\Actions;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\FontWeight;

class ViewClass extends ViewRecord
{
    protected static string $resource = ClassResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Subject Information')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('subject.code')
                        ->label('Subject Code')
                        ->weight(FontWeight::Bold),
                    TextEntry::make('subject.title')->label('Subject Title'),
                    TextEntry::make('section')->badge()->color('primary'),
                ]),
            ]),

            Section::make('Weekly Schedule')
                ->collapsible()
                ->schema([
                    ViewEntry::make('schedule')->view(
                        'filament.faculty.components.weekly-schedule',
                        [
                            'class' => $this->record,
                            'schedule' => $this->record->formatted_weekly_schedule,
                            'days' => [
                                'Monday',
                                'Tuesday',
                                'Wednesday',
                                'Thursday',
                                'Friday',
                                'Saturday',
                            ],
                            'showEmptyDays' => true,
                            'showRoomDetails' => true,
                        ]
                    ),
                ]),

            Section::make('Room Information')
                ->schema([
                    TextEntry::make('room.name')
                        ->label('Assigned Room')
                        ->badge()
                        ->color('gray'),
                    TextEntry::make('room.building')
                        ->label('Building')
                        ->visible(fn ($record) => $record->room?->building),
                    TextEntry::make('room.capacity')
                        ->label('Room Capacity')
                        ->visible(fn ($record) => $record->room?->capacity),
                ])
                ->columns(3),

            Section::make('Enrollment Information')->schema([
                Grid::make(3)->schema([
                    TextEntry::make('enrollments_count')
                        ->label('Enrolled Students')
                        ->formatStateUsing(
                            fn ($record) => $record->enrollments()->count().
                                ' / '.
                                $record->maximum_slots
                        ),

                    TextEntry::make('finalized_grades')
                        ->label('Finalized Grades')
                        ->formatStateUsing(
                            fn ($record) => $record
                                ->enrollments()
                                ->where('is_grades_finalized', true)
                                ->count()
                        )
                        ->color('success'),

                    TextEntry::make('pending_grades')
                        ->label('Pending Grades')
                        ->formatStateUsing(
                            fn ($record) => $record
                                ->enrollments()
                                ->where('is_grades_finalized', false)
                                ->count()
                        )
                        ->color('warning'),
                ]),
            ]),
        ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('manage_grades')
                ->label('Gradebook')
                ->icon('heroicon-m-clipboard-document-list')
                ->url(
                    fn () => ClassGradeManagementResource::getUrl('index', [
                        'class_id' => $this->record->id,
                    ])
                )
                ->color('warning'),

            Actions\Action::make('view_students')
                ->label('Student Roster')
                ->icon('heroicon-m-users')
                ->url(
                    fn () => StudentResource::getUrl('index', [
                        'class_id' => $this->record->id,
                    ])
                ),
        ];
    }
}
