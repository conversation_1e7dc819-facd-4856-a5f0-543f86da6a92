<?php

namespace App\Filament\Faculty\Resources\ClassGradeManagementResource\Pages;

use App\Filament\Faculty\Resources\ClassGradeManagementResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditGrades extends EditRecord
{
    protected static string $resource = ClassGradeManagementResource::class;

    // protected static bool $shouldSkipAuthorization = true;
    protected function getHeaderActions(): array
    {
        return [
            // Removed Delete Action
            // Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
