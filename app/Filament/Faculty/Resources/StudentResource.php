<?php

namespace App\Filament\Faculty\Resources;

use App\Filament\Faculty\Resources\StudentResource\Pages;
use App\Models\Student;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class StudentResource extends Resource
{
    protected static ?string $model = Student::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?string $navigationLabel = 'Student Roster';

    protected static ?int $navigationSort = 3;

    protected static bool $shouldRegisterNavigation = false; // Hide from main nav

    protected static bool $shouldSkipAuthorization = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Student Information')
                    ->schema([
                        Forms\Components\TextInput::make('full_name')
                            ->label('Name')
                            ->disabled(),
                        Forms\Components\TextInput::make('student_id')
                            ->disabled(),
                        Forms\Components\TextInput::make('course.name')
                            ->label('Program')
                            ->disabled(),
                        Forms\Components\TextInput::make('formatted_academic_year')
                            ->label('Year Level')
                            ->disabled(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('full_name')
                    ->searchable()
                    ->sortable()
                    ->description(fn ($record) => $record->student_id),

                TextColumn::make('course.name')
                    ->label('Program')
                    ->badge()
                    ->color('primary'),

                TextColumn::make('formatted_academic_year')
                    ->label('Year Level')
                    ->badge()
                    ->color('gray'),

                TextColumn::make('status')
                    ->badge()
                    ->color(fn ($state) => match ($state) {
                        'active' => 'success',
                        'inactive' => 'warning',
                        'graduated' => 'primary',
                        default => 'gray'
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('class')
                    ->label('Filter by Class')
                    ->relationship('classEnrollments.class', 'section')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'graduated' => 'Graduated',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye'),
            ])
            ->bulkActions([])
            ->defaultSort('last_name')
            ->deferLoading();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStudents::route('/'),
            'view' => Pages\ViewStudent::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereHas('classEnrollments.class', function ($query): void {
                $query->where('faculty_id', auth()->id());
            })
            ->when(
                request()->has('class_id'),
                fn ($query) => $query->whereHas('classEnrollments', fn ($q) => $q->where('class_id', request('class_id')))
            );
    }
}
