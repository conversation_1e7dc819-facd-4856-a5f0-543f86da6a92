<?php

namespace App\Filament\Faculty\Components;

use App\Models\Classes;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class WeeklySchedule extends Component
{
    public array $schedules = [];

    public array $days = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
    ];

    public ?Classes $class = null;

    public bool $showEmptyDays = true;

    public bool $showRoomDetails = true;

    public function __construct(
        ?Classes $class = null,
        $schedules = null,
        bool $showEmptyDays = true,
        bool $showRoomDetails = true
    ) {
        $this->class = $class;
        $this->showEmptyDays = $showEmptyDays;
        $this->showRoomDetails = $showRoomDetails;

        if ($schedules) {
            $this->schedules = $schedules;
        } elseif ($class) {
            $this->schedules = $class->formatted_weekly_schedule;
        }
    }

    public function render(): View
    {
        $displayDays = $this->showEmptyDays
            ? $this->days
            : $this->filterEmptyDays();

        return view('filament.faculty.components.weekly-schedule', [
            'days' => $displayDays,
            'schedule' => $this->schedules,
            'showRoomDetails' => $this->showRoomDetails,
            'class' => $this->class,
        ]);
    }

    private function filterEmptyDays(): array
    {
        return array_filter($this->days, function ($day) {
            $dayLower = strtolower($day);

            return isset($this->schedules[$dayLower]) &&
                count($this->schedules[$dayLower]) > 0;
        });
    }
}
