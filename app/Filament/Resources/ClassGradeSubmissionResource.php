<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClassGradeSubmissionResource\Pages\ListClassGradeSubmissions;
use App\Filament\Resources\ClassGradeSubmissionResource\Pages\ViewClassGradeSubmission;
use App\Models\Classes;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

// use App\Filament\Resources\ClassGradeSubmissionResource;
class ClassGradeSubmissionResource extends Resource
{
    protected static ?string $model = Classes::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?string $navigationLabel = 'Class Grade Submissions';

    protected static ?string $pluralModelLabel = 'Grade Submitions';

    public static function form(Form $form): Form
    {
        return $form->schema([
            // Form schema not needed as we won't be editing classes here
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('subject_code')
                    ->label('Subject')
                    ->description(fn ($record) => $record->subject_title)
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('section')
                    ->label('Section')
                    ->searchable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('faculty.full_name')
                    ->label('Faculty')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('academic_year')
                    ->label('Academic Year')
                    ->sortable(),

                Tables\Columns\TextColumn::make('semester')
                    ->label('Semester')
                    ->formatStateUsing(
                        fn ($state) => match ($state) {
                            '1' => '1st Semester',
                            '2' => '2nd Semester',
                            default => $state,
                        }
                    )
                    ->sortable(),

                Tables\Columns\TextColumn::make('grade_status')
                    ->label('Grade Status')
                    ->formatStateUsing(function ($record) {
                        $total = $record->enrollments()->count();
                        $finalized = $record
                            ->enrollments()
                            ->where('is_grades_finalized', true)
                            ->count();
                        $verified = $record
                            ->enrollments()
                            ->where('is_grades_verified', true)
                            ->count();

                        return "{$verified} verified / {$finalized} finalized / {$total} total";
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('faculty_id')
                    ->label('Faculty')
                    ->options(function () {
                        // Get unique faculty IDs from classes that have faculty assigned
                        $facultyIds = \App\Models\Classes::whereNotNull('faculty_id')
                            ->distinct()
                            ->pluck('faculty_id')
                            ->toArray();

                        return \App\Models\Faculty::whereIn('id', $facultyIds)
                            ->get()
                            ->mapWithKeys(function ($faculty) {
                                return [$faculty->id => $faculty->full_name];
                            })
                            ->toArray();
                    })
                    ->searchable(),

                Tables\Filters\SelectFilter::make('academic_year')->options(
                    fn () => Classes::distinct('academic_year')
                        ->whereNotNull('academic_year')
                        ->where('academic_year', '!=', '')
                        ->pluck('academic_year', 'academic_year')
                        ->toArray()
                ),

                Tables\Filters\SelectFilter::make('semester')->options([
                    '1' => '1st Semester',
                    '2' => '2nd Semester',
                ]),

                Tables\Filters\SelectFilter::make('verification_status')
                    ->options([
                        'complete' => 'Completely Verified',
                        'partial' => 'Partially Verified',
                        'none' => 'No Verification',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query
                            ->when($data['value'] === 'complete', function (
                                $q
                            ): void {
                                $q->whereHas(
                                    'enrollments',
                                    function ($sq): void {
                                        $sq->where('is_grades_verified', true);
                                    },
                                    '=',
                                    $q->withCount('enrollments')->getQuery()
                                );
                            })
                            ->when($data['value'] === 'partial', function ($q): void {
                                $q->whereHas('enrollments', function ($sq): void {
                                    $sq->where('is_grades_verified', true);
                                })->whereHas('enrollments', function ($sq): void {
                                    $sq->where('is_grades_verified', false);
                                });
                            })
                            ->when($data['value'] === 'none', function ($q): void {
                                $q->whereDoesntHave('enrollments', function (
                                    $sq
                                ): void {
                                    $sq->where('is_grades_verified', true);
                                });
                            });
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\Action::make('view_grades')
                    ->label('Manage Grades')
                    ->icon('heroicon-o-check')
                    ->url(
                        fn (Classes $record) => route(
                            'filament.admin.resources.grade-approvals.index',
                            [
                                'tableFilters[class_id][value]' => $record->id,
                            ]
                        )
                    ),

                Tables\Actions\Action::make('verify_all')
                    ->label('Verify All Pending')
                    ->icon('heroicon-o-shield-check')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(
                        fn (Classes $record) => $record
                            ->enrollments()
                            ->where('is_grades_finalized', true)
                            ->where('is_grades_verified', false)
                            ->exists()
                    )
                    ->action(function (Classes $record): void {
                        $record
                            ->enrollments()
                            ->where('is_grades_finalized', true)
                            ->where('is_grades_verified', false)
                            ->update([
                                'is_grades_verified' => true,
                                'verified_by' => auth()->id(),
                                'verified_at' => now(),
                            ]);
                    }),
            ])
            ->bulkActions([
                // No bulk actions needed
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListClassGradeSubmissions::route('/'),
            'view' => ViewClassGradeSubmission::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->whereNotNull('faculty_id');
    }
}
