<?php

namespace App\Filament\Resources\ClassResource\RelationManagers;

use App\Models\Classes;
use App\Models\Student;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;

class ClassEnrollmentsRelationManager extends RelationManager
{
    protected static string $relationship = 'class_enrollments';

    protected static ?string $recordTitleAttribute = 'student_id';

    protected static ?string $title = 'Enrolled Students';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('student_id')
                    ->label('Student')
                    ->options(fn () => Student::all()->pluck('full_name', 'id'))
                    ->searchable()
                    ->required()
                    ->preload()
                    ->columnSpan('full'),

                Grid::make(3)
                    ->schema([
                        TextInput::make('prelim_grade')
                            ->label('Prelim')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->live()
                            ->afterStateUpdated(fn (callable $set) => $set('total_average', null)), // Recalculate average

                        TextInput::make('midterm_grade')
                            ->label('Midterm')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->live()
                            ->afterStateUpdated(fn (callable $set) => $set('total_average', null)), // Recalculate average

                        TextInput::make('finals_grade')
                            ->label('Finals')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->live()
                            ->afterStateUpdated(fn (callable $set) => $set('total_average', null)), // Recalculate average
                    ]),

                Grid::make(2)
                    ->schema([
                        TextInput::make('total_average')
                            ->label('Final Grade')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->disabled()
                            ->placeholder(function (callable $get) {
                                // Calculate average only if all grades are present
                                $prelim = $get('prelim_grade');
                                $midterm = $get('midterm_grade');
                                $finals = $get('finals_grade');

                                if ($prelim !== null && $midterm !== null && $finals !== null) {
                                    $average = ($prelim + $midterm + $finals) / 3;

                                    return number_format($average, 2);
                                }

                                return 'N/A';
                            }),

                        Select::make('status')
                            ->options([
                                true => 'Passed',
                                false => 'Failed',
                            ])
                            ->default(true),
                    ]),

                Textarea::make('remarks')
                    ->rows(2)
                    ->columnSpan('full'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('student_id')
            ->columns([
                TextColumn::make('student_id')
                    ->label('Student')
                    ->formatStateUsing(fn ($record) => Student::find($record->student_id)?->full_name ?? 'N/A')
                    ->searchable(query: function ($query, $search) {
                        return $query->whereHas('student', function ($q) use ($search): void {
                            $q->where('first_name', 'like', "%{$search}%")
                                ->orWhere('last_name', 'like', "%{$search}%");
                        });
                    })
                    ->sortable(),

                TextColumn::make('prelim_grade')
                    ->label('Prelim')
                    ->sortable(),

                TextColumn::make('midterm_grade')
                    ->label('Midterm')
                    ->sortable(),

                TextColumn::make('finals_grade')
                    ->label('Finals')
                    ->sortable(),

                TextColumn::make('total_average')
                    ->label('Final Grade')
                    ->sortable(),

                IconColumn::make('status')
                    ->boolean()
                    ->label('Status')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                TextColumn::make('remarks')
                    ->limit(30)
                    ->tooltip(fn (TextColumn $column) => $column->getState()),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        '1' => 'Passed',
                        '0' => 'Failed',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Enroll Student')
                    ->modalHeading('Enroll New Student')
                    ->modalWidth('lg'),
            ])
            ->actions([
                Tables\Actions\Action::make('Move')
                    ->requiresConfirmation()
                    ->modalHeading('Move This Student to another class')
                    ->modalDescription(
                        'Are you sure you\'d like to Move this student to another Class? This cannot be undone.'
                    )
                    ->icon('heroicon-o-arrow-right-on-rectangle')
                    ->label('Move to a Class')
                    ->form([
                        Select::make('moveClass')
                            ->label('Classes')
                            ->hint(
                                'Select a Section you want this student to move to '
                            )
                            ->options(
                                Classes::where(
                                    'subject_code',
                                    $this->getOwnerRecord()->subject_code
                                )
                                    ->whereNot(
                                        'id',
                                        $this->getOwnerRecord()->id
                                    )
                                    ->get()
                                    ->pluck('section', 'id')
                            ),
                    ])
                    ->action(function (array $data, $record): void {
                        $record->class_id = $data['moveClass'];
                        $record->save();
                    }),
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edit Student Enrollment')
                    ->modalWidth('lg'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('Move')
                        ->requiresConfirmation()
                        ->modalHeading('Move This Students to another class')
                        ->modalDescription(
                            'Are you sure you\'d like to move this student to another class? This cannot be undone.'
                        )
                        ->icon('heroicon-o-arrow-right-on-rectangle')
                        ->label('Move to a class')
                        ->form([
                            Select::make('moveClass1')
                                ->label('Classes')
                                ->options(Classes::where('subject_code', $this->getOwnerRecord()->subject_code)->get()->pluck('section', 'id')),
                        ])
                        ->action(function (array $data, Collection $records): void {
                            $records->each(function ($record) use ($data): void {
                                $record->update([
                                    'class_id' => $data['moveClass1'],
                                ]);
                            });
                        }),
                ]),
            ]);
    }
}
