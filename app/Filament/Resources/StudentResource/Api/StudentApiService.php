<?php

namespace App\Filament\Resources\StudentResource\Api;

use App\Filament\Resources\StudentResource;
use Rupadana\ApiService\ApiService;

class StudentApiService extends ApiService
{
    protected static ?string $resource = StudentResource::class;

    public static function handlers(): array
    {
        return [
            Handlers\CreateHandler::class,
            Handlers\UpdateHandler::class,
            Handlers\DeleteHandler::class,
            Handlers\PaginationHandler::class,
            Handlers\DetailHandler::class,
        ];

    }
}
