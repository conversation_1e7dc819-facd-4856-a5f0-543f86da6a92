<?php

namespace App\Filament\Resources\StudentResource\Api\Handlers;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Api\Requests\UpdateStudentRequest;
use Rupadana\ApiService\Http\Handlers;

class UpdateHandler extends Handlers
{
    public static ?string $uri = '/{id}';

    public static ?string $resource = StudentResource::class;

    public static function getMethod()
    {
        return Handlers::PUT;
    }

    public static function getModel()
    {
        return static::$resource::getModel();
    }

    /**
     * Update Student
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function handler(UpdateStudentRequest $request)
    {
        $id = $request->route('id');

        $model = static::getModel()::find($id);

        if (! $model) {
            return static::sendNotFoundResponse();
        }

        $model->fill($request->all());

        $model->save();

        return static::sendSuccessResponse($model, 'Successfully Update Resource');
    }
}
