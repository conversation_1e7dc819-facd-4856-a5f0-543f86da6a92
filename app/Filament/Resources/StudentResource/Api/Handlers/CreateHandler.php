<?php

namespace App\Filament\Resources\StudentResource\Api\Handlers;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Api\Requests\CreateStudentRequest;
use Rupadana\ApiService\Http\Handlers;

class CreateHandler extends Handlers
{
    public static ?string $uri = '/';

    public static ?string $resource = StudentResource::class;

    public static function getMethod()
    {
        return Handlers::POST;
    }

    public static function getModel()
    {
        return static::$resource::getModel();
    }

    /**
     * Create Student
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function handler(CreateStudentRequest $request)
    {
        $model = new (static::getModel());

        $model->fill($request->all());

        $model->save();

        return static::sendSuccessResponse($model, 'Successfully Create Resource');
    }
}
