<?php

namespace App\Filament\Resources\StudentResource\RelationManagers;

use App\Models\Students;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ClassesRelationManager extends RelationManager
{
    protected static string $relationship = 'Classes';

    protected static bool $isLazy = false;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),

            ]);
    }

    public function table(Table $table): Table
    {
        $record = $this->getOwnerRecord();

        return $table
            ->recordTitleAttribute('id')
            ->columns([

                Tables\Columns\TextColumn::make('class.subject.code')
                    ->label('Subject Code'),

                Tables\Columns\TextColumn::make('class.subject.title')
                    ->label('Subject Title'),
                Tables\Columns\TextColumn::make('class.subject.units')
                    ->label('Units'),
                Tables\Columns\TextColumn::make('class.section')
                    ->label('Section'),
                Tables\Columns\TextColumn::make('class.Schedule.room.name')
                    ->label('Room'),
                Tables\Columns\TextColumn::make('class.academic_year')
                    ->label('Academic Year'),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
                Tables\Actions\Action::make('ReEnroll Class')
                    ->action(function ($record): void {
                        $student = Students::find($this->getOwnerRecord()->id);
                        $student->autoEnrollInClasses();
                    }),

            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('Subject Details')
                            ->schema([
                                TextEntry::make('class.subject.code')
                                    ->label('Subject Code'),
                                TextEntry::make('class.subject.title')
                                    ->label('Subject Title'),
                            ]),
                        Tabs\Tab::make('Class Details')
                            ->schema([
                                TextEntry::make('class.section')
                                    ->label('Section'),
                                TextEntry::make('class.academic_year')
                                    ->label('Academic Year'),
                            ]),
                        Tabs\Tab::make('Schedule Details')
                            ->schema([
                                TextEntry::make('class.Schedule.room.name')
                                    ->label('Room'),
                                TextEntry::make('class.Schedule.day_of_week')
                                    ->label('Schedule Day'),
                                TextEntry::make('class.Schedule.start_time')
                                    ->label('Start Time'),
                                TextEntry::make('class.Schedule.end_time')
                                    ->label('End Time'),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public function isReadOnly(): bool
    {
        return false;
    }
}
