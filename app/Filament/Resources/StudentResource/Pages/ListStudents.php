<?php

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Widgets\StudentsWidget;
use App\Jobs\StudentCreateJob;
use App\Models\SubjectEnrolled;
use CodeWithDennis\FactoryAction\FactoryAction;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use JoseEspinal\RecordNavigation\Traits\HasRecordsList;

class ListStudents extends ListRecords
{
    // use HasRecordsList;

    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // FactoryAction::make()
            //     ->color('danger')
            //     // ->job(StudentCreateJob::class)
            // // ->slideOver()
            //     ->icon('heroicon-o-wrench')
            //     ->hasMany([SubjectEnrolled::class]),
            Actions\CreateAction::make(),
        ];
    }

    protected function getCounts()
    {
        return cache()->remember('student_counts', 24 * 3600, function () {
            return DB::table('students')
                ->select([
                    DB::raw('COUNT(*) as total'),
                    DB::raw("SUM(CASE WHEN courses.code LIKE 'BSIT%' AND students.academic_year != 5 THEN 1 ELSE 0 END) as bsit"),
                    DB::raw("SUM(CASE WHEN courses.code LIKE 'BSHM%' AND students.academic_year != 5 THEN 1 ELSE 0 END) as bshm"),
                    DB::raw("SUM(CASE WHEN courses.code LIKE 'BSBA%' AND students.academic_year != 5 THEN 1 ELSE 0 END) as bsba"),
                    DB::raw('SUM(CASE WHEN students.academic_year = 5 THEN 1 ELSE 0 END) as graduates'),
                ])
                ->join('courses', 'students.course_id', '=', 'courses.id')
                ->first();
        });
    }

    public function getTabs(): array
    {
        $counts = $this->getCounts();

        $tabs = [
            'all' => Tab::make('All')
                ->badge($counts->total),
            'BSIT' => Tab::make('BSIT')
                ->badge($counts->bsit),
            'BSHM' => Tab::make('BSHM')
                ->badge($counts->bshm),
            'BSBA' => Tab::make('BSBA')
                ->badge($counts->bsba),
            'Graduates' => Tab::make('Graduates')
                ->badge($counts->graduates),
        ];

        return $tabs;
    }

    protected function getHeaderWidgets(): array
    {

        return [
            StudentsWidget::class,
        ];
    }

    public function getHeaderWidgetsColumns(): int|array
    {
        return 2;
    }

    protected function getCachedHeaderWidgets(): ?array
    {
        return [
            \Filament\Widgets\StatsOverviewWidget::class => [
                // Your widget configuration here, if any
            ],
        ];
    }

    protected function getTableQuery(): Builder
    {
        return parent::getTableQuery()
            ->with(['course:id,code'])
            ->select(['students.id', 'students.first_name', 'students.last_name', 'students.course_id', 'students.gender', 'students.academic_year', 'students.email']);
    }

    protected function getTableFilters(): array
    {
        return [
            SelectFilter::make('course')
                ->options([
                    'BSIT' => 'BSIT',
                    'BSHM' => 'BSHM',
                    'BSBA' => 'BSBA',
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['value'],
                            fn (Builder $query, $course): Builder => $query->whereHas('course', fn (Builder $q) => $q->where('code', 'like', $course.'%'))
                        );
                }),
            SelectFilter::make('academic_year')
                ->options([
                    '1' => '1st Year',
                    '2' => '2nd Year',
                    '3' => '3rd Year',
                    '4' => '4th Year',
                    '5' => 'Graduates',
                ])
                ->query(function (Builder $query, array $data): Builder {
                    return $query
                        ->when(
                            $data['value'],
                            fn (Builder $query, $year): Builder => $query->where('students.academic_year', $year)
                        );
                }),
        ];
    }
}
