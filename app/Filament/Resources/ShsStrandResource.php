<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShsStrandResource\Pages;
use App\Filament\Resources\ShsStrandResource\RelationManagers; // Ensure this line is present and uncommented
use App\Models\ShsStrand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ShsStrandResource extends Resource
{
    protected static ?string $model = ShsStrand::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Senior High School';

    protected static ?int $navigationSort = 2;

    protected static ?string $recordTitleAttribute = 'strand_name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('track_id')
                    ->relationship('track', 'track_name')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->reactive()
                    ->label('Track')
                    ->createOptionForm([
                        Forms\Components\TextInput::make('track_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull(),
                    ])
                    ->createOptionAction(function (Forms\Components\Actions\Action $action) {
                        return $action
                            ->modalWidth('lg')
                            ->modalButton('Create Track');
                    })
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('strand_name')
                    ->required()
                    ->maxLength(255)
                    ->label('Strand Name'),
                Forms\Components\Textarea::make('description')
                    ->maxLength(65535)
                    ->columnSpanFull()
                    ->label('Description'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('strand_name')
                    ->searchable()
                    ->sortable()
                    ->label('Strand Name'),
                Tables\Columns\TextColumn::make('track.track_name')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->label('Track'),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->tooltip(fn (ShsStrand $record): ?string => $record->description)
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('students_count')->counts('students')
                    ->label('No. of Students')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('track_id')
                    ->relationship('track', 'track_name')
                    ->searchable()
                    ->preload()
                    ->label('Filter by Track')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\StudentsRelationManager::class, // Added this line
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShsStrands::route('/'),
            'create' => Pages\CreateShsStrand::route('/create'),
            'edit' => Pages\EditShsStrand::route('/{record}/edit'),
            'view' => Pages\ViewShsStrand::route('/{record}'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'info';
    }
}
