<?php

namespace App\Filament\Resources\ClassGradeSubmissionResource\Pages;

use App\Filament\Resources\ClassGradeSubmissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListClassGradeSubmissions extends ListRecords
{
    protected static string $resource = ClassGradeSubmissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('verify_all_pending')
                ->label('Verify All Pending Grades')
                ->icon('heroicon-o-shield-check')
                ->color('success')
                ->requiresConfirmation()
                ->action(function (): void {
                    $count = \App\Models\ClassEnrollment::query()
                        ->where('is_grades_finalized', true)
                        ->where('is_grades_verified', false)
                        ->update([
                            'is_grades_verified' => true,
                            'verified_by' => auth()->id(),
                            'verified_at' => now(),
                        ]);

                    $this->notify(
                        'success',
                        "{$count} grade submissions have been verified."
                    );
                    $this->refreshTable();
                }),
        ];
    }
}
