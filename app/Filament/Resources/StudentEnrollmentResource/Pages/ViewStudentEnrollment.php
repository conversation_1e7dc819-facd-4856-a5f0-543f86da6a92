<?php

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Enums\AcademicYear;
use App\Enums\EnrollStat;
use App\Filament\Resources\StudentEnrollmentResource;
use App\Models\GeneralSetting;
use App\Models\StudentEnrollment;
use App\Services\EnrollmentService;
use Exception;
use Filament\Actions;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\Actions\Action as InfolistAction;
use Filament\Infolists\Components\Actions as InfolistActions;
use Filament\Infolists\Components\Fieldset;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Icetalker\FilamentTableRepeatableEntry\Infolists\Components\TableRepeatableEntry;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Saade\FilamentAutograph\Forms\Components\Enums\DownloadableFormat;
use Saade\FilamentAutograph\Forms\Components\SignaturePad;

class ViewStudentEnrollment extends ViewRecord
{
    protected static string $resource = StudentEnrollmentResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Fieldset::make('Student Information')
                    ->columns(3)
                    ->columnSpan(3)
                    ->schema([
                        TextEntry::make('student_id')
                            ->badge()
                            ->copyable()
                            ->icon('phosphor-clipboard')
                            ->label('Student ID'),

                        TextEntry::make('student.full_name')->label(
                            'Student Full name'
                        ),
                        TextEntry::make('student.email')->label(
                            'Student Email'
                        ),
                        TextEntry::make('student.academic_year')
                            ->label('Year Level')
                            ->formatStateUsing(function ($state) {
                                return match ($state) {
                                    1 => AcademicYear::first,
                                    2 => AcademicYear::second,
                                    3 => AcademicYear::third,
                                    4 => AcademicYear::fourth,
                                    default => $state,
                                };
                            }),
                        TextEntry::make('student.course.code')
                            ->badge()
                            ->label('Course'),
                        InfolistSection::make('Subjects Enrolled')
                            ->collapsed()
                            ->schema([
                                TableRepeatableEntry::make('subjectsEnrolled')
                                    ->columns(4)
                                    ->schema([
                                        TextEntry::make('subject.code')->label(
                                            'Subject Code'
                                        ),
                                        TextEntry::make('subject.title')
                                            ->label('Subject Title')
                                            ->words(3),
                                        TextEntry::make('subject.units')->label(
                                            'Units'
                                        ),
                                        TextEntry::make('school_year')->label(
                                            'School Year'
                                        ),
                                        TextEntry::make('semester')->label(
                                            'Semester'
                                        ),
                                    ])
                                    ->label('Subjects Enrolled'),
                            ]),

                        InfolistSection::make("Enrollee's Schedule")
                            ->collapsed()
                            ->schema([
                                ViewEntry::make('classSchedule')->view(
                                    'infolists.components.enrollee-sched'
                                ),
                            ]),
                    ]),
                Fieldset::make("Enrollee's Status")
                    ->columnSpan(1)
                    ->schema([
                        TextEntry::make('status')
                            ->label('Status')
                            ->badge()
                            ->columnSpanFull()
                            ->icon(function ($record) {
                                return match ($record->status) {
                                    EnrollStat::Pending->value => 'phosphor-x-fill',
                                    EnrollStat::VerifiedByDeptHead->value => 'phosphor-check-square-offset',
                                    EnrollStat::VerifiedByCashier->value => 'phosphor-check-circle',
                                    default => 'phosphor-question-mark-light',
                                };
                            })
                            ->color(function ($record) {
                                return match ($record->status) {
                                    EnrollStat::Pending->value => 'warning',
                                    EnrollStat::VerifiedByDeptHead->value => 'success',
                                    EnrollStat::VerifiedByCashier->value => 'primary',
                                };
                            }),
                        ViewEntry::make('signature.depthead_signature')
                            ->label('Department head Signature')
                            ->visible(
                                fn ($record) => isset(
                                    $record->signature->depthead_signature
                                )
                            )
                            ->columnSpanFull()
                            ->view('infolists.components.signature-view'),
                        ViewEntry::make('signature.registrar_signature')
                            ->label('Registrar Signature')
                            ->visible(
                                fn ($record) => isset(
                                    $record->signature->registrar_signature
                                )
                            )
                            ->view('infolists.components.signature-view'),
                        ViewEntry::make('signature.cashier_signature')
                            ->label('Cashier Signature')
                            ->visible(
                                fn ($record) => isset(
                                    $record->signature->cashier_signature
                                )
                            )
                            ->view('infolists.components.signature-view'),
                        TextEntry::make('studentTuition.discount')
                            ->label('Discount')
                            ->columnSpanFull()
                            ->prefix('%'),
                        TextEntry::make('studentTuition.total_lectures')
                            ->label('Total Lecture Fee')
                            ->columnSpanFull()
                            ->prefix('₱'),
                        TextEntry::make('studentTuition.total_laboratory')
                            ->label('Total Laboratory Fee')
                            ->columnSpanFull()
                            ->prefix('₱'),
                        TextEntry::make(
                            'studentTuition.total_miscelaneous_fees'
                        )
                            ->label('Total Miscellaneous Fee')
                            ->columnSpanFull()
                            ->prefix('₱'),
                        TextEntry::make('studentTuition.overall_tuition')
                            ->label('Overall Tuition Fee')
                            ->columnSpanFull()
                            ->prefix('₱'),
                        TextEntry::make('studentTuition.downpayment')
                            ->label('Down Payment')
                            ->columnSpanFull()
                            ->prefix('₱')
                            ->tooltip('Reapply Transaction')
                            ->suffixAction(
                                InfolistAction::make('Reapply Downpayment')
                                    ->icon('heroicon-m-arrow-uturn-left')
                                    ->requiresConfirmation()
                                    ->action(function ($record) {
                                        DB::beginTransaction();
                                        try {
                                            $tuition = $record->studentTuition;
                                            $previous_balance =
                                                $tuition->total_balance;
                                            $tuition->total_balance -=
                                                $tuition->downpayment;
                                            $tuition->save();
                                            Notification::make(
                                                'Successfully Reapplied Transaction'
                                            )
                                                ->success('success')
                                                ->body(
                                                    'Balance: ₱'.
                                                        $tuition->total_balance.
                                                        ' has been updated'.
                                                        'Previous Balance: ₱'.
                                                        $previous_balance
                                                )
                                                ->send();
                                            DB::commit();
                                        } catch (Exception $e) {
                                            DB::rollBack();
                                            Notification::make(
                                                'Failed to Reapply Transaction'
                                            )
                                                ->danger('danger')
                                                ->body($e->getMessage())
                                                ->send();
                                        }

                                        return $record;
                                    })
                            ),
                        TextEntry::make('studentTuition.total_balance')
                            ->label('Balance')
                            ->columnSpanFull()
                            ->prefix('₱'),
                    ]),
                Fieldset::make('Resources')
                    ->columnSpanFull()
                    ->schema([
                        RepeatableEntry::make('resources')
                            ->schema([
                                TextEntry::make('type')->badge()->color(
                                    fn (string $state): string => match (
                                        $state
                                    ) {
                                        'assessment' => 'success',
                                        'certificate' => 'warning',
                                        default => 'gray',
                                    }
                                ),
                                TextEntry::make('file_name'),
                                TextEntry::make('file_size')->formatStateUsing(
                                    fn ($state) => number_format(
                                        $state / 1024,
                                        2
                                    ).' KB'
                                ),
                                TextEntry::make('created_at')->dateTime(),
                                InfolistActions::make([
                                    InfolistAction::make('view')
                                        ->icon('heroicon-m-eye')
                                        ->url(
                                            fn ($record) => route(
                                                'filament.admin.resources.student-enrollments.view-resource',
                                                [
                                                    'record' => $record->resourceable_id,
                                                    'resourceId' => $record->id,
                                                ]
                                            )
                                        )
                                        ->openUrlInNewTab(),
                                    InfolistAction::make('delete')
                                        ->icon('heroicon-m-trash')
                                        ->color('danger')
                                        ->requiresConfirmation()
                                        ->action(function ($record): void {
                                            Storage::disk(
                                                $record->disk
                                            )->delete($record->file_path);
                                            $record->delete();
                                        }),
                                ]),
                            ])
                            ->columnSpanFull()
                            ->columns(5),
                    ]),
            ])
            ->columns(4);
    }

    protected function getHeaderActions(): array
    {
        return [
            // Primary Actions
            Actions\Action::make('verifyAsHeadDept')
                ->label('Verify as Dept Head')
                ->icon('heroicon-o-check')
                ->color('primary')
                ->visible(
                    fn (StudentEnrollment $record): bool => $record->status ===
                        EnrollStat::Pending->value &&
                        (Auth::user()->can(
                            'verify_by_head_dept_guest::enrollment'
                        ) ||
                            Auth::user()->hasRole('super_admin'))
                )
                ->form(function () {
                    $generalSettings = GeneralSetting::first();
                    if ($generalSettings?->enable_signatures == true) {
                        return [
                            SignaturePad::make('signature')
                                ->label(__('Sign here'))
                                ->dotSize(2.0)
                                ->lineMinWidth(0.5)
                                ->lineMaxWidth(2.5)
                                ->throttle(16)
                                ->minDistance(5)
                                ->velocityFilterWeight(0.7)
                                ->filename('autograph')
                                ->downloadable()
                                ->downloadableFormats([
                                    DownloadableFormat::PNG,
                                    DownloadableFormat::JPG,
                                ])
                                ->backgroundColor('rgba(0,0,0,0)')
                                ->backgroundColorOnDark('#000')
                                ->exportBackgroundColor('#fff')
                                ->penColor('#000')
                                ->penColorOnDark('#fff')
                                ->exportPenColor('#000')
                                ->downloadActionDropdownPlacement('center-end'),
                        ];
                    }

                    return [];
                })
                ->requiresConfirmation()
                ->action(function (
                    StudentEnrollment $record,
                    array $data,
                    EnrollmentService $enrollmentService
                ): void {
                    $signature = $data['signature'] ?? null;
                    $success = $enrollmentService->verifyByHeadDept(
                        $record,
                        $signature
                    );
                    if (! $success) {
                        $this->halt();
                    }
                    $this->refreshFormData([
                        'status',
                        'signature.depthead_signature',
                    ]);
                }),

            Actions\Action::make('verifyAsCashier')
                ->label('Enroll This Student')
                ->icon('heroicon-o-check')
                ->color('success')
                ->modalHeading('Pending Payment')
                ->modalDescription(
                    'This student has not yet paid the down payment. Please enter the amount of the down payment.'
                )
                ->form(function ($record) {
                    $formComponents = [
                        KeyValue::make('settlements')
                            ->label('Settlements')
                            ->columnSpanFull()
                            ->helperText(
                                'Enter the settlements for the following.'
                            )
                            ->default([
                                'registration_fee' => 0,
                                'tuition_fee' => $record->studentTuition?->downpayment ?? 0,
                                'miscelanous_fee' => 0,
                                'diploma_or_certificate' => 0,
                                'transcript_of_records' => 0,
                                'certification' => 0,
                                'special_exam' => 0,
                                'others' => 0,
                            ])
                            ->reorderable()
                            ->editableKeys(false)
                            ->keyLabel('Particulars')
                            ->deletable(false)
                            ->addable(false)
                            ->valueLabel('Ammounts')
                            ->required(),
                        TextInput::make('invoicenumber')
                            ->label('Invoice Number')
                            ->required(),
                    ];
                    $generalSettings = GeneralSetting::first();
                    if ($generalSettings?->enable_signatures == true) {
                        $formComponents[] = SignaturePad::make('signature')
                            ->label(__('Sign here'))
                            ->dotSize(2.0)
                            ->lineMinWidth(0.5)
                            ->lineMaxWidth(2.5)
                            ->throttle(16)
                            ->minDistance(5)
                            ->velocityFilterWeight(0.7)
                            ->filename('autograph')
                            ->downloadable()
                            ->downloadableFormats([
                                DownloadableFormat::PNG,
                                DownloadableFormat::JPG,
                            ])
                            ->backgroundColor('rgba(0,0,0,0)')
                            ->backgroundColorOnDark('#000')
                            ->exportBackgroundColor('#fff')
                            ->penColor('#000')
                            ->penColorOnDark('#fff')
                            ->exportPenColor('#000')
                            ->downloadActionDropdownPlacement('center-end');
                    }

                    return $formComponents;
                })
                ->visible(
                    fn (StudentEnrollment $record): bool => ($record->status ===
                        EnrollStat::VerifiedByCashier->value ||
                        $record->status ===
                            EnrollStat::VerifiedByDeptHead->value) &&
                        (Auth::user()->can(
                            'verify_by_cashier_guest::enrollment'
                        ) ||
                            Auth::user()->hasRole('super_admin'))
                )
                ->action(function (
                    StudentEnrollment $record,
                    array $data,
                    EnrollmentService $enrollmentService
                ) {
                    $success = $enrollmentService->verifyByCashier(
                        $record,
                        $data
                    );
                    if ($success) {
                        return redirect()->route(
                            'filament.admin.resources.students.index'
                        );
                    } else {
                        $this->halt();
                    }
                }),

            // Dropdown for other actions
            ActionGroup::make([
                Actions\EditAction::make(),
                Actions\Action::make('retryClassEnrollment')
                    ->label('Retry Class Enrollment')
                    ->icon('heroicon-o-arrow-path')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Retry Class Enrollment?')
                    ->modalDescription(
                        'This will attempt to re-enroll the student in all classes for the subjects in this enrollment. Force enrollment is enabled by default to override maximum class size limits.'
                    )
                    ->form([
                        \Filament\Forms\Components\Toggle::make(
                            'force_enrollment'
                        )
                            ->label('Force Enrollment')
                            ->helperText(
                                'Override maximum class size limits when enrolling'
                            )
                            ->default(true),
                    ])
                    ->action(function (
                        array $data,
                        StudentEnrollment $record
                    ): void {
                        $originalConfigValue = config(
                            'enrollment.force_enroll_when_full'
                        );
                        if ($data['force_enrollment']) {
                            config([
                                'enrollment.force_enroll_when_full' => true,
                            ]);
                        }
                        try {
                            $student = $record->student;
                            if (! $student) {
                                Notification::make()
                                    ->danger()
                                    ->title('Student Not Found')
                                    ->body(
                                        'The student associated with this enrollment could not be found.'
                                    )
                                    ->send();

                                return;
                            }
                            $student->autoEnrollInClasses($record->id);
                            Notification::make()
                                ->success()
                                ->title('Enrollment Retry Complete')
                                ->body(
                                    'The system has attempted to enroll the student in all classes. Check the notification for results.'
                                )
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->danger()
                                ->title('Enrollment Retry Failed')
                                ->body('An error occurred: '.$e->getMessage())
                                ->send();
                        } finally {
                            if ($data['force_enrollment']) {
                                config([
                                    'enrollment.force_enroll_when_full' => $originalConfigValue,
                                ]);
                            }
                        }
                    }),

                Actions\Action::make('undoCashierVerification')
                    ->label('Undo Cashier Verification')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Undo Cashier Verification?')
                    ->modalDescription(
                        'This will restore the enrollment and revert the status to "Verified By Dept Head". Financial transactions will NOT be automatically reversed and may require manual correction. Proceed?'
                    )
                    ->visible(function (StudentEnrollment $record): bool {
                        $currentRecord = StudentEnrollment::withTrashed()->find(
                            $record->id
                        );

                        return ($currentRecord->trashed() ||
                            $currentRecord->status ===
                                EnrollStat::VerifiedByCashier->value) &&
                            (Auth::user()->can(
                                'verify_by_cashier_guest::enrollment'
                            ) ||
                                Auth::user()->hasRole('super_admin'));
                    })
                    ->action(function (
                        StudentEnrollment $record,
                        EnrollmentService $enrollmentService
                    ): void {
                        $success = $enrollmentService->undoCashierVerification(
                            $record->id
                        );
                        if (! $success) {
                            $this->halt();
                        }
                        $this->refreshFormData([
                            'status',
                            'signature.cashier_signature',
                        ]);
                        $this->dispatch('refresh');
                    })
                    ->disabled(
                        fn (
                            StudentEnrollment $record
                        ): bool => ! $record->trashed() &&
                            $record->status !==
                                EnrollStat::VerifiedByCashier->value
                    ),

                Actions\Action::make('undoHeadDeptVerification')
                    ->label('Undo Head Dept Verification')
                    ->icon('heroicon-o-arrow-uturn-left')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->modalHeading('Undo Head Dept Verification?')
                    ->modalDescription(
                        'This will revert the status to "Pending" and remove the Head Dept signature. Proceed?'
                    )
                    ->visible(
                        fn (
                            StudentEnrollment $record
                        ): bool => $record->status ===
                            EnrollStat::VerifiedByDeptHead->value &&
                            (Auth::user()->can(
                                'verify_by_head_dept_guest::enrollment'
                            ) ||
                                Auth::user()->hasRole('super_admin'))
                    )
                    ->action(function (
                        StudentEnrollment $record,
                        EnrollmentService $enrollmentService
                    ): void {
                        $success = $enrollmentService->undoHeadDeptVerification(
                            $record
                        );
                        if (! $success) {
                            $this->halt();
                        }
                        $this->refreshFormData([
                            'status',
                            'signature.depthead_signature',
                        ]);
                    }),

                Actions\Action::make('Resend Assessment')
                    ->label('Resend Assessment')
                    ->icon('heroicon-o-envelope')
                    ->color('primary')
                    ->visible(
                        fn (
                            StudentEnrollment $record
                        ): bool => $record->status ===
                            EnrollStat::VerifiedByCashier->value &&
                            (Auth::user()->can(
                                'verify_by_cashier_guest::enrollment'
                            ) ||
                                Auth::user()->hasRole('super_admin'))
                    )
                    ->requiresConfirmation()
                    ->modalHeading('Resend Assessment Notification')
                    ->modalDescription(
                        fn (
                            StudentEnrollment $record
                        ): string => "Are you sure you want to resend the assessment notification to {$record->student->first_name} {$record->student->last_name} ({$record->student->email})?\n\nThis will generate a new PDF assessment form and send it via email."
                    )
                    ->modalSubmitActionLabel('Yes, Resend Assessment')
                    ->modalIcon('heroicon-o-envelope')
                    ->action(function (
                        StudentEnrollment $record,
                        EnrollmentService $enrollmentService
                    ): void {
                        $result = $enrollmentService->resendAssessmentNotification(
                            $record
                        );
                        if ($result['success']) {
                            Notification::make()
                                ->title('Assessment Resend Queued')
                                ->success()
                                ->body(
                                    "Assessment notification has been queued for {$result['student_name']}. The process is running in the background and you will receive a notification when completed."
                                )
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Assessment Resend Failed')
                                ->danger()
                                ->body($result['message'])
                                ->send();
                            $this->halt();
                        }
                    }),

                Actions\Action::make('View Assessment')
                    ->label('View Assessment')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->url(
                        fn (): string => route('assessment.download', [
                            'record' => $this->record->id,
                        ])
                    )
                    ->openUrlInNewTab(true),
            ])
                ->label('More Options')
                ->icon('heroicon-m-ellipsis-vertical')
                ->size(\Filament\Support\Enums\ActionSize::Small)
                ->color('gray')
                ->button(),
        ];
    }
}
