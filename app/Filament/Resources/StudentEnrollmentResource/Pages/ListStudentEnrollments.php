<?php

namespace App\Filament\Resources\StudentEnrollmentResource\Pages;

use App\Filament\Resources\StudentEnrollmentResource;
use App\Filament\Resources\StudentEnrollmentResource\Widgets\AcademicYearStatsWidget;
use App\Filament\Resources\StudentEnrollmentResource\Widgets\EnrollmentStatsWidget;
use App\Filament\Resources\StudentEnrollmentResource\Widgets\StudentEnrollmentStatsWidget;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListStudentEnrollments extends ListRecords
{
    protected static string $resource = StudentEnrollmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getHeaderWidgets(): array
    {
        return [
            StudentEnrollmentStatsWidget::class,
            // EnrollmentStatsWidget::class,
            // AcademicYearStatsWidget::class,
        ];
    }

    public function getTabs(): array
    {
        return static::getResource()::getTabs();
    }

    public function getFooterWidgets(): array
    {
        return [
            AcademicYearStatsWidget::class,
        ];
    }
}
