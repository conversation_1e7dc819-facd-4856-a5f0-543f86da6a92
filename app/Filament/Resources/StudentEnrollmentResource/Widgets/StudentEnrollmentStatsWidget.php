<?php

namespace App\Filament\Resources\StudentEnrollmentResource\Widgets;

use App\Models\GeneralSetting; // Import GeneralSetting
use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB; // Add this import

class StudentEnrollmentStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected int $sortOrder = 1;

    protected function getStats(): array
    {
        // Fetch current academic period settings using the service
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString() ?? 'unknown_sy';
        $currentSemester = $settingsService->getCurrentSemester() ?? 'unknown_sem';
        $cacheKey = "enrollment_stats_{$currentSchoolYear}_{$currentSemester}";

        // Cache the stats for 5 minutes using the dynamic key
        return Cache::remember($cacheKey, 300, function () use ($settingsService, $currentSchoolYear, $currentSemester) {
            // Use the same values inside the closure
            $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
            $currentSemester = $settingsService->getCurrentSemester();

            // Base query scoped to the current academic period
            $baseQuery = StudentEnrollment::query()->currentAcademicPeriod();
            // Decide whether to include trashed records in stats
            // $baseQuery->withTrashed();

            // Use a single query to get all status counts for the current period
            $statusCounts = (clone $baseQuery) // Clone scoped query
                ->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // Calculate stats based on the scoped query
            $totalEnrollments = (clone $baseQuery)->count(); // Count only for current period
            $pendingEnrollments = $statusCounts['Pending'] ?? 0;
            $verifiedByDeptHead = $statusCounts['Verified By Department Head'] ?? 0;
            $verifiedByCashier = $statusCounts['Verified By Cashier'] ?? 0; // Get from statusCounts

            // Get financial stats *only for the current academic period*
            $financialStats = DB::table('student_tuition')
                ->join('student_enrollment', 'student_tuition.enrollment_id', '=', 'student_enrollment.id')
                ->when($currentSchoolYear, fn ($q) => $q->where('student_enrollment.school_year', $currentSchoolYear))
                ->when($currentSemester, fn ($q) => $q->where('student_enrollment.semester', $currentSemester))
                // ->whereNull('student_enrollment.deleted_at') // Optionally exclude deleted enrollments' tuition
                ->select(
                    DB::raw('SUM(student_tuition.overall_tuition) as total_tuition'),
                    DB::raw('SUM(student_tuition.total_balance) as total_balance')
                )
                ->first();

            // Get semester counts *only for the current school year* (semester breakdown doesn't make sense here)
            // This stat might need rethinking - maybe show total for current semester?
            // For now, let's show total for the current period
            // $semesterCounts = (clone $baseQuery)
            //     ->select('semester', DB::raw('count(*) as count'))
            //     ->groupBy('semester')
            //     ->pluck('count', 'semester')
            //     ->toArray();

            $currentPeriodLabel = $currentSchoolYear && $currentSemester
                ? $currentSchoolYear.' - '.($currentSemester == 1 ? '1st Sem' : '2nd Sem')
                : 'Current Period';

            return [
                Stat::make('Enrollments ('.$currentPeriodLabel.')', $totalEnrollments)
                    ->description('Enrollments in the current period')
                    ->descriptionIcon('heroicon-m-academic-cap')
                    ->color('primary'),

                Stat::make('Pending ('.$currentPeriodLabel.')', $pendingEnrollments)
                    ->description('Awaiting verification')
                    ->descriptionIcon('heroicon-m-clock')
                    ->color('warning'),

                Stat::make('Verified by Head ('.$currentPeriodLabel.')', $verifiedByDeptHead)
                    ->description('Passed department verification')
                    ->descriptionIcon('heroicon-m-check-badge')
                    ->color('info'), // Changed color slightly

                Stat::make('Verified by Cashier ('.$currentPeriodLabel.')', $verifiedByCashier)
                    ->description('Fully verified enrollments')
                    ->descriptionIcon('heroicon-m-check-circle')
                    ->color('success'),

                Stat::make('Total Tuition ('.$currentPeriodLabel.')', '₱ '.number_format($financialStats->total_tuition ?? 0, 2))
                    ->description('Assessed tuition for the period')
                    ->descriptionIcon('heroicon-m-banknotes')
                    ->color('primary'),

                Stat::make('Total Balance ('.$currentPeriodLabel.')', '₱ '.number_format($financialStats->total_balance ?? 0, 2))
                    ->description('Remaining balance for the period')
                    ->descriptionIcon('heroicon-m-calculator')
                    ->color(($financialStats->total_balance ?? 0) > 0 ? 'danger' : 'success'),

                // Removed the semester breakdown stat as it's less relevant when scoped
                // Stat::make("$currentSchoolYear Enrollments", ($semesterCounts['1'] ?? 0) . ' | ' . ($semesterCounts['2'] ?? 0))
                //     ->description('1st Semester | 2nd Semester')
                //     ->descriptionIcon('heroicon-m-calendar')
                //     ->color('info'),
            ];
        });
    }
}
