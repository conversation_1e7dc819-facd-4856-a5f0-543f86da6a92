<?php

namespace App\Filament\Resources\GradeApprovalResource\Pages;

use App\Filament\Resources\GradeApprovalResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;

class EditGradeApproval extends EditRecord
{
    protected static string $resource = GradeApprovalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('recalculate')
                ->icon('heroicon-o-calculator')
                ->color('warning')
                ->action(function (): void {
                    if (
                        is_numeric($this->record->prelim_grade) &&
                        is_numeric($this->record->midterm_grade) &&
                        is_numeric($this->record->finals_grade)
                    ) {
                        $average = round(
                            ($this->record->prelim_grade + $this->record->midterm_grade + $this->record->finals_grade) / 3,
                            2
                        );
                        $this->record->update([
                            'total_average' => $average,
                            'remarks' => $average >= 75 ? 'PASSED' : 'FAILED',
                        ]);

                        Notification::make()
                            ->title('Grades have been recalculated')
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('Cannot recalculate')
                            ->body('Some grades are missing or invalid')
                            ->warning()
                            ->send();
                    }
                }),

            Actions\DeleteAction::make()
                ->visible(fn () => Auth::user()->hasRole('registrar') || Auth::user()->hasRole('super_admin')),

            Actions\Action::make('view')
                ->url(fn () => $this->getResource()::getUrl('view', ['record' => $this->record->id]))
                ->icon('heroicon-o-eye'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Calculate total average if all three grades are present
        if (
            isset($data['prelim_grade']) &&
            isset($data['midterm_grade']) &&
            isset($data['finals_grade']) &&
            is_numeric($data['prelim_grade']) &&
            is_numeric($data['midterm_grade']) &&
            is_numeric($data['finals_grade'])
        ) {
            $data['total_average'] = round(($data['prelim_grade'] + $data['midterm_grade'] + $data['finals_grade']) / 3, 2);
            $data['remarks'] = $data['total_average'] >= 75 ? 'PASSED' : 'FAILED';
        }

        return $data;
    }
}
