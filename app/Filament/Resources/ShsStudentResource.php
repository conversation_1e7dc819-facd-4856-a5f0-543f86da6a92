<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShsStudentResource\Pages;
use App\Models\ShsStrand;
use App\Models\ShsStudent;
use App\Models\ShsTrack;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder; // Required for dependent select
use Illuminate\Database\Eloquent\Model; // Required for dependent select
use Illuminate\Support\Collection; // Required for dependent select

class ShsStudentResource extends Resource
{
    protected static ?string $model = ShsStudent::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Senior High School';

    protected static ?int $navigationSort = 3;

    protected static ?string $recordTitleAttribute = 'fullname';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Student Information')
                ->columns(2)
                ->schema([
                    Forms\Components\TextInput::make('student_lrn')
                        ->label('LRN (Learner Reference Number)')
                        ->maxLength(20) // Typical LRN length
                        ->unique(
                            ShsStudent::class,
                            'student_lrn',
                            ignoreRecord: true
                        )
                        ->columnSpanFull(),
                    Forms\Components\TextInput::make('fullname')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\DatePicker::make('birthdate')
                        ->native(false)
                        ->maxDate(now()->subYears(10)) // Sensible max date for SHS
                        ->label('Birthdate'),
                    Forms\Components\Select::make('gender')
                        ->options([
                            'Male' => 'Male',
                            'Female' => 'Female',
                        ])
                        ->native(false),
                    Forms\Components\TextInput::make('email')
                        ->email()
                        ->maxLength(255)
                        ->label('Email Address')
                        ->unique(
                            ShsStudent::class,
                            'email',
                            ignoreRecord: true
                        ),
                    Forms\Components\TextInput::make('student_contact')
                        ->maxLength(20) // Adjusted for various formats
                        ->tel()
                        ->label('Student Contact No.'),
                    Forms\Components\TextInput::make('religion')->maxLength(
                        100
                    ),
                    Forms\Components\TextInput::make('nationality')->maxLength(
                        100
                    ),
                    Forms\Components\Select::make('civil_status')
                        ->options([
                            'Single' => 'Single',
                            'Married' => 'Married', // Though less common for SHS
                            'Other' => 'Other',
                        ])
                        ->native(false),
                ]),

            Forms\Components\Section::make('Enrollment Details')
                ->columns(2)
                ->schema([
                    Forms\Components\Select::make('grade_level')
                        ->options([
                            'Grade 11' => 'Grade 11',
                            'Grade 12' => 'Grade 12',
                        ])
                        ->required()
                        ->native(false),
                    Forms\Components\Select::make('track_id')
                        ->relationship('track', 'track_name')
                        ->searchable()
                        ->preload()
                        ->live() // Important for dependent select
                        ->afterStateUpdated(function (Set $set): void {
                            $set('strand_id', null); // Reset strand when track changes
                        })
                        ->required()
                        ->label('Track')
                        ->createOptionForm([
                            Forms\Components\TextInput::make('track_name')
                                ->required()
                                ->maxLength(255),
                            Forms\Components\Textarea::make(
                                'description'
                            )->columnSpanFull(),
                        ])
                        ->createOptionAction(function (
                            Forms\Components\Actions\Action $action
                        ) {
                            return $action
                                ->modalWidth('lg')
                                ->modalButton('Create Track');
                        })
                        ->columnSpanFull(),
                    Forms\Components\Select::make('strand_id')
                        ->label('Strand')
                        ->options(
                            fn (Get $get): Collection => ShsStrand::query()
                                ->where('track_id', $get('track_id'))
                                ->pluck('strand_name', 'id')
                        )
                        ->searchable()
                        ->preload()
                        ->live()
                        // Not strictly required, as some tracks might not have strands (e.g. Sports, Arts)
                        // ->required(fn (Get $get): bool => ShsTrack::find($get('track_id'))?->strands()->count() > 0)
                        ->placeholder('Select a strand (if applicable)')
                        ->native(false)
                        ->createOptionForm(
                            fn (Get $get): array => [
                                // Pass track_id to modal
                                Forms\Components\Hidden::make(
                                    'track_id'
                                )->default($get('track_id')),
                                Forms\Components\TextInput::make(
                                    'track_display'
                                )
                                    ->label('Track')
                                    ->default(
                                        fn (): ?string => ShsTrack::find(
                                            $get('track_id')
                                        )?->track_name
                                    )
                                    ->disabled(),
                                Forms\Components\TextInput::make('strand_name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Textarea::make(
                                    'description'
                                )->columnSpanFull(),
                            ]
                        )
                        ->createOptionUsing(function (array $data, Get $get) {
                            $trackId = $get('track_id'); // Get track_id from main form context
                            if (! $trackId) {
                                // Handle error: track_id not found in context
                                // This might happen if the create form is opened in a way that context is lost
                                // For now, return null or throw an exception
                                return null;
                            }
                            $data['track_id'] = $trackId; // Ensure track_id from main form is used

                            return ShsStrand::create($data);
                        })
                        ->createOptionAction(function (
                            Forms\Components\Actions\Action $action
                        ) {
                            return $action
                                ->modalWidth('lg')
                                ->modalButton('Create Strand');
                        })
                        ->columnSpanFull(),
                ]),

            Forms\Components\Section::make('Guardian Information')
                ->columns(2)
                ->schema([
                    Forms\Components\TextInput::make(
                        'guardian_name'
                    )->maxLength(255),
                    Forms\Components\TextInput::make('guardian_contact')
                        ->maxLength(20)
                        ->tel()
                        ->label('Guardian Contact No.'),
                ]),

            Forms\Components\Section::make('Address')->schema([
                Forms\Components\Textarea::make('complete_address')
                    ->maxLength(65535) // Max length for TEXT type
                    ->columnSpanFull(),
            ]),

            Forms\Components\Section::make('Other Information')->schema([
                Forms\Components\Textarea::make('remarks')
                    ->maxLength(65535)
                    ->columnSpanFull(),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->sortable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('student_lrn')
                    ->label('LRN')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fullname')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('grade_level')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('track.track_name')
                    ->label('Track')
                    ->badge()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('strand.strand_name')
                    ->label('Strand')
                    ->badge()
                    ->color('success')
                    ->searchable()
                    ->sortable()
                    ->placeholder('N/A'),
                Tables\Columns\TextColumn::make('gender')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('student_contact')
                    ->label('Contact No.')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('grade_level')
                    ->options([
                        'Grade 11' => 'Grade 11',
                        'Grade 12' => 'Grade 12',
                    ])
                    ->native(false),
                Tables\Filters\SelectFilter::make('track_id')
                    ->relationship('track', 'track_name')
                    ->searchable()
                    ->preload()
                    ->label('Filter by Track')
                    ->native(false),
                Tables\Filters\SelectFilter::make('strand_id')
                    // ->relationship('strand', 'strand_name') // This might become complex if dependent on track filter
                    ->label('Filter by Strand')
                    ->options(
                        fn (): array => ShsStrand::pluck(
                            'strand_name',
                            'id'
                        )->all()
                    )
                    ->searchable()
                    ->preload()
                    ->native(false),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make(
                            'created_from'
                        )->native(false),
                        Forms\Components\DatePicker::make(
                            'created_until'
                        )->native(false),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (
                                    Builder $query,
                                    $date
                                ): Builder => $query->whereDate(
                                    'created_at',
                                    '>=',
                                    $date
                                )
                            )
                            ->when(
                                $data['created_until'],
                                fn (
                                    Builder $query,
                                    $date
                                ): Builder => $query->whereDate(
                                    'created_at',
                                    '<=',
                                    $date
                                )
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // Relation managers can be added here if needed for the View/Edit pages,
            // e.g., a list of enrolled subjects or grades.
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShsStudents::route('/'),
            'create' => Pages\CreateShsStudent::route('/create'),
            'view' => Pages\ViewShsStudent::route('/{record}'),
            'edit' => Pages\EditShsStudent::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function getGlobalSearchResultTitle(Model $record): string
    {
        return $record->fullname.' ('.$record->student_lrn.')';
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['fullname', 'student_lrn', 'email'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        $details = [
            'Track' => $record->track?->track_name,
        ];
        if ($record->strand) {
            $details['Strand'] = $record->strand->strand_name;
        }

        return $details;
    }
}
