<?php

namespace App\Filament\Resources;

use App\Enums\SubjectEnrolledEnum;
use App\Filament\Resources\SubjectResource\Pages;
use App\Models\Subject;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class SubjectResource extends Resource
{
    protected static ?string $model = Subject::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Academic Management';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Subject Details')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Section::make('Subject Information')
                                    ->description('Enter the core details for the subject.')
                                    ->schema([
                                        Forms\Components\TextInput::make('code')
                                            ->required()
                                            ->maxLength(255)
                                            ->unique(ignoreRecord: true)
                                            ->label('Subject Code')
                                            ->helperText('Unique code for the subject (e.g., IT101).'),
                                        Forms\Components\TextInput::make('title')
                                            ->required()
                                            ->maxLength(255)
                                            ->label('Subject Title')
                                            ->helperText('Full title of the subject.'),
                                        Forms\Components\Textarea::make('description')
                                            ->maxLength(255)
                                            ->label('Description')
                                            ->helperText('Brief description of the subject.')
                                            ->columnSpanFull(),
                                        Select::make('classification')
                                            ->required()
                                            ->options(SubjectEnrolledEnum::class)
                                            ->label('Classification')
                                            ->helperText('Classification of the subject.'),
                                        Forms\Components\TextInput::make('units')
                                            ->required()
                                            ->numeric()
                                            ->label('Units')
                                            ->helperText('Number of units for the subject.'),
                                        Forms\Components\TextInput::make('lecture')
                                            ->numeric()
                                            ->label('Lecture Hours')
                                            ->helperText('Number of lecture hours.'),
                                        Forms\Components\TextInput::make('laboratory')
                                            ->numeric()
                                            ->label('Laboratory Hours')
                                            ->helperText('Number of laboratory hours.'),
                                        Forms\Components\TextInput::make('pre_riquisite')
                                            ->label('Pre-requisite')
                                            ->helperText('Subject ID of Pre-requisite'),
                                        Forms\Components\Checkbox::make('is_credited')
                                            ->label('Is Credited')
                                            ->helperText('Check if this subject is credited.'),
                                    ])->columns(2),
                            ]),
                        Tabs\Tab::make('Scheduling')
                            ->schema([
                                Section::make('Academic Details')
                                    ->description('Specify academic year, semester, course, and grouping.')
                                    ->schema([
                                        Forms\Components\Select::make('course_id')
                                            ->relationship('course', 'code')
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->label('Course')
                                            ->helperText('Select the course this subject belongs to.'),
                                        Forms\Components\Select::make('academic_year')
                                            ->options([
                                                1 => '1st Year',
                                                2 => '2nd Year',
                                                3 => '3rd Year',
                                                4 => '4th Year',
                                            ])
                                            ->label('Academic Year')
                                            ->helperText('Academic year for the subject.'),
                                        Forms\Components\Select::make('semester')
                                            ->options([
                                                1 => '1st Semester',
                                                2 => '2nd Semester',
                                                3 => 'Summer',
                                            ])
                                            ->label('Semester')
                                            ->helperText('Semester for the subject.'),
                                        Forms\Components\TextInput::make('group')
                                            ->maxLength(255)
                                            ->label('Group')
                                            ->helperText('Group, if applicable.'),
                                    ])->columns(2),
                            ]),
                    ])->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->label('Code'),
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->label('Title'),
                Tables\Columns\TextColumn::make('course.code')
                    ->searchable()
                    ->sortable()
                    ->label('Course'),
                Tables\Columns\TextColumn::make('units')
                    ->sortable()
                    ->label('Units'),
                Tables\Columns\TextColumn::make('academic_year')
                    ->sortable()
                    ->label('Academic Year'),
                Tables\Columns\TextColumn::make('semester')
                    ->sortable()
                    ->label('Semester'),
                Tables\Columns\IconColumn::make('is_credited')
                    ->boolean()
                    ->label('Credited'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('course')
                    ->relationship('course', 'code')
                    ->label('Filter by Course')
                    ->searchable(),
                SelectFilter::make('academic_year')
                    ->options([
                        1 => '1st Year',
                        2 => '2nd Year',
                        3 => '3rd Year',
                        4 => '4th Year',
                    ])
                    ->label('Filter by Academic Year'),
                SelectFilter::make('semester')
                    ->options([
                        1 => '1st Semester',
                        2 => '2nd Semester',
                        3 => 'Summer',
                    ])
                    ->label('Filter by Semester'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubjects::route('/'),
            'create' => Pages\CreateSubject::route('/create'),
            'edit' => Pages\EditSubject::route('/{record}/edit'),
            'view' => Pages\ViewSubject::route('/{record}'),
        ];
    }
}
