<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionResource\Pages;
use App\Models\Student;
use App\Models\Transaction;
use Filament\Forms;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\FontWeight;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

// Corrected Class Definition
class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'Transactions';

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DateTimePicker::make('transaction_date')
                    ->required(),
                Forms\Components\TextInput::make('invoicenumber')
                    ->maxLength(255)
                    ->default(null),
                KeyValue::make('settlements')
                    ->label('Settlements')
                    ->columnSpanFull()
                    ->helperText(
                        'Enter the settlements for the following.'
                    )
                    ->default([
                        'registration_fee' => 0,
                        'tuition_fee' => 0,
                        'miscelanous_fee' => 0,
                        'diploma_or_certificate' => 0,
                        'transcript_of_records' => 0,
                        'certification' => 0,
                        'special_exam' => 0,
                        'others' => 0,
                    ])
                    ->reorderable()
                    ->editableKeys(false)
                    ->keyLabel('Particulars')
                    ->deletable(false)
                    ->addable(false)
                    ->valueLabel('Ammounts')
                    ->required(),
                Forms\Components\Textarea::make('signature')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            // Eager load student data for efficiency, explicitly selecting needed columns
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'studentTransactions.student' => function ($query): void {
                    $query->select('id', 'first_name', 'last_name', 'middle_name'); // Select only necessary student columns
                },
            ]))
            ->columns([
                Tables\Columns\TextColumn::make('studentTransactions.student_id')
                    ->label('student. No.')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false), // Show by default

                // Combine Student ID and Name, potentially linkable if StudentResource exists
                Tables\Columns\TextColumn::make('student_full_name')
                    ->label('Student')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        // Custom search logic for student ID or name parts, now case-insensitive for names
                        return $query->whereHas('studentTransactions.student', function (Builder $q) use ($search): void {
                            $lowerSearch = strtolower($search);
                            $q->where('id', 'like', "%{$search}%") // Keep ID search case-sensitive (or as is)
                                ->orWhereRaw('LOWER(first_name) LIKE ?', ["%{$lowerSearch}%"]) // Case-insensitive first name search
                                ->orWhereRaw('LOWER(last_name) LIKE ?', ["%{$lowerSearch}%"]); // Case-insensitive last name search
                        });
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        // Sorting by related student name requires a join or more complex logic
                        // For simplicity, let's disable sorting on this combined column for now
                        // If needed, implement a join in modifyQueryUsing
                        return $query; // No sorting for now
                    }),

                Tables\Columns\TextColumn::make('description')
                    ->searchable()
                    ->wrap(), // Wrap long descriptions

                Tables\Columns\TextColumn::make('total_amount')
                    ->label('Amount')
                    ->money('PHP') // Assuming PHP currency
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        // Sorting by total_amount requires calculating it in the query
                        // This can be complex depending on how 'settlements' is stored (JSON)
                        // Let's disable sorting for now, or implement raw DB expression if needed.
                        return $query; // No sorting for now
                    }),
                // ->summarize(Sum::make()->label('Total')->money('PHP')), // Removed summarizer causing SQL error

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match (strtolower($state)) {
                        'pending' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('transaction_date')
                    ->dateTime('M d, Y h:i A') // More readable format
                    ->sortable(),

                Tables\Columns\TextColumn::make('invoicenumber')
                    ->label('Invoice No.')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true), // Hide by default

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true), // Hide by default

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true), // Hide by default
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),
                // Reinstate manual date range filter
                Filter::make('transaction_date')
                    ->form([
                        Forms\Components\DatePicker::make('transaction_date_from')
                            ->label('Transaction Date From'),
                        Forms\Components\DatePicker::make('transaction_date_to')
                            ->label('Transaction Date To'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['transaction_date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['transaction_date_to'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(), // Keep standard actions
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Transaction Details')
                    ->columns(3) // Use 3 columns for better layout
                    ->schema([
                        TextEntry::make('transaction_number')
                            ->label('Transaction No.')
                            ->weight(FontWeight::Bold),
                        TextEntry::make('status')
                            ->badge()
                            ->color(fn (string $state): string => match (strtolower($state)) {
                                'pending' => 'warning',
                                'completed' => 'success',
                                'cancelled' => 'danger',
                                default => 'gray',
                            }),
                        TextEntry::make('transaction_date')
                            ->label('Transaction Date')
                            ->dateTime('M d, Y h:i A'),
                        TextEntry::make('description')
                            ->columnSpanFull(), // Let description take full width
                        TextEntry::make('invoicenumber')
                            ->label('Invoice No.'),
                        TextEntry::make('created_at')
                            ->dateTime('M d, Y h:i A'),
                        TextEntry::make('updated_at')
                            ->dateTime('M d, Y h:i A'),
                    ]),

                Section::make('Student Information')
                    // ->relationship('studentTransactions.student') // Access student via relationship
                    ->columns(2)
                    ->schema([
                        // Use accessors defined in Transaction model if they fetch correctly
                        // Otherwise, access via $get('studentTransactions.0.student.attribute')
                        TextEntry::make('student_id') // Accessor from Transaction model
                            ->label('Student ID')
                            ->copyable()
                            ->icon('fas-copy'),
                        TextEntry::make('student_full_name') // Accessor from Transaction model
                            ->label('Student Name'),
                        TextEntry::make('student_course') // Accessor from Transaction model
                            ->label('Course & Year')
                            ->badge(),
                        TextEntry::make('student_email') // Accessor from Transaction model
                            ->label('Email')
                            ->copyable()
                            ->icon('fas-copy'),
                        TextEntry::make('student_personal_contact') // Accessor from Transaction model
                            ->label('Contact Number'),
                    ]),

                Section::make('Settlements')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\KeyValueEntry::make('settlements')
                            ->label('Particulars & Amounts')
                            ->columnSpanFull()
                            ->valueLabel('Amount')
                            ->keyLabel('Particular'),
                        // ->money('PHP', shouldConvert: false) // Format values as money
                        // ->numeric(decimalPlaces: 2), // Ensure numeric formatting
                        TextEntry::make('total_amount')
                            ->label('Total Amount')
                            ->money('PHP')
                            ->weight(FontWeight::Bold)
                            ->alignEnd(), // Align total to the right
                    ]),

                Section::make('Signature')
                    ->collapsible() // Make it collapsible if it takes space
                    ->schema([
                        Infolists\Components\ImageEntry::make('signature')
                            ->hiddenLabel()
                            ->visibility('private') // Assuming signature might be sensitive
                            ->height(100), // Set a reasonable height
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            'create' => Pages\CreateTransaction::route('/create'),
            'view' => Pages\ViewTransaction::route('/{record}'),
            'edit' => Pages\EditTransaction::route('/{record}/edit'),
        ];
    }
}
