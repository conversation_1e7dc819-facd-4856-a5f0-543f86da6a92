<?php

namespace App\Filament\Resources\FacultyResource\Pages;

use App\Filament\Resources\FacultyResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFaculties extends ListRecords
{
    protected static string $resource = FacultyResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->icon('heroicon-o-plus'),
            Actions\Action::make('manageAssignments')
                ->label('Manage Class Assignments')
                ->icon('heroicon-o-academic-cap')
                ->color('info')
                ->url(fn () => FacultyResource::getUrl('manage-assignments')),
        ];
    }
}
