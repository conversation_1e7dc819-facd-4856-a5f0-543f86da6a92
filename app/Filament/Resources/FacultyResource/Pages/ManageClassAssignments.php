<?php

namespace App\Filament\Resources\FacultyResource\Pages;

use App\Filament\Resources\FacultyResource;
use App\Models\Classes;
use App\Models\Faculty;
use App\Services\GeneralSettingsService;
use Filament\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;

class ManageClassAssignments extends Page
{
    protected static string $resource = FacultyResource::class;

    protected static string $view = 'filament.resources.faculty-resource.pages.manage-class-assignments';

    protected static ?string $title = 'Manage Class Assignments';

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        Select::make('academic_period')
                            ->label('Academic Period')
                            ->options(function () {
                                $settingsService = app(GeneralSettingsService::class);
                                $currentYear = $settingsService->getCurrentSchoolYearString();
                                $currentSemester = $settingsService->getCurrentSemester();

                                return [
                                    'current' => "Current: {$currentYear} - ".($currentSemester == 1 ? '1st' : '2nd').' Semester',
                                    'all' => 'All Periods',
                                ];
                            })
                            ->default('current')
                            ->live()
                            ->afterStateUpdated(fn () => $this->resetAssignments()),

                        Select::make('filter_type')
                            ->label('Show Classes')
                            ->options([
                                'unassigned' => 'Unassigned Only',
                                'all' => 'All Classes',
                                'assigned' => 'Assigned Only',
                            ])
                            ->default('unassigned')
                            ->live()
                            ->afterStateUpdated(fn () => $this->resetAssignments()),
                    ]),

                Repeater::make('assignments')
                    ->label('Class Assignments')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                Select::make('class_id')
                                    ->label('Class')
                                    ->searchable()
                                    ->preload()
                                    ->options(function (callable $get) {
                                        return $this->getAvailableClasses($get('../../academic_period'), $get('../../filter_type'));
                                    })
                                    ->live()
                                    ->afterStateUpdated(function ($state, callable $set): void {
                                        if ($state) {
                                            $class = Classes::find($state);
                                            if ($class && $class->faculty_id) {
                                                $set('faculty_id', $class->faculty_id);
                                            }
                                        }
                                    }),

                                Select::make('faculty_id')
                                    ->label('Assign to Faculty')
                                    ->searchable()
                                    ->preload()
                                    ->options(function () {
                                        return Faculty::where('status', 'active')
                                            ->get()
                                            ->pluck('full_name', 'id');
                                    })
                                    ->placeholder('Select Faculty Member'),
                            ]),
                    ])
                    ->addActionLabel('Add Class Assignment')
                    ->reorderable(false)
                    ->collapsible()
                    ->itemLabel(function (array $state): ?string {
                        if (! $state['class_id']) {
                            return null;
                        }

                        $class = Classes::find($state['class_id']);
                        if (! $class) {
                            return null;
                        }

                        $faculty = $state['faculty_id'] ? Faculty::find($state['faculty_id']) : null;
                        $facultyName = $faculty ? $faculty->full_name : 'Unassigned';

                        return "{$class->subject_title} - {$class->section} → {$facultyName}";
                    }),
            ])
            ->statePath('data');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('autoAssign')
                ->label('Auto-Assign Classes')
                ->icon('heroicon-o-sparkles')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Auto-Assign Unassigned Classes')
                ->modalDescription('This will automatically distribute unassigned classes among active faculty members. Continue?')
                ->action(function (): void {
                    $this->autoAssignClasses();
                }),

            Action::make('saveAssignments')
                ->label('Save All Assignments')
                ->icon('heroicon-o-check')
                ->color('primary')
                ->action(function (): void {
                    $this->saveAssignments();
                }),

            Action::make('loadUnassigned')
                ->label('Load Unassigned Classes')
                ->icon('heroicon-o-plus')
                ->color('info')
                ->action(function (): void {
                    $this->loadUnassignedClasses();
                }),
        ];
    }

    protected function getAvailableClasses(?string $period = 'current', ?string $filterType = 'unassigned'): array
    {
        $query = Classes::query();

        if ($period === 'current') {
            $query->currentAcademicPeriod();
        }

        switch ($filterType) {
            case 'unassigned':
                $query->whereNull('faculty_id');
                break;
            case 'assigned':
                $query->whereNotNull('faculty_id');
                break;
                // 'all' doesn't add any filter
        }

        return $query->get()
            ->mapWithKeys(function ($class) {
                $type = $class->isShs() ? 'SHS' : 'College';
                $info = $class->isShs() ? $class->formatted_track_strand : $class->formatted_course_codes;
                $label = "{$class->subject_title} - {$class->section} ({$type})";
                if ($info && $info !== 'N/A') {
                    $label .= " - {$info}";
                }

                $facultyInfo = $class->faculty_id ? " [Assigned to: {$class->Faculty?->full_name}]" : ' [Unassigned]';
                $label .= $facultyInfo;

                return [$class->id => $label];
            })
            ->toArray();
    }

    public function resetAssignments(): void
    {
        $this->data['assignments'] = [];
    }

    public function loadUnassignedClasses(): void
    {
        $unassignedClasses = Classes::currentAcademicPeriod()
            ->whereNull('faculty_id')
            ->get();

        $assignments = [];
        foreach ($unassignedClasses as $class) {
            $assignments[] = [
                'class_id' => $class->id,
                'faculty_id' => null,
            ];
        }

        $this->data['assignments'] = $assignments;

        Notification::make()
            ->title('Unassigned Classes Loaded')
            ->body("Loaded {$unassignedClasses->count()} unassigned classes")
            ->info()
            ->send();
    }

    public function autoAssignClasses(): void
    {
        $unassignedClasses = Classes::currentAcademicPeriod()
            ->whereNull('faculty_id')
            ->get();

        $activeFaculty = Faculty::where('status', 'active')->get();

        if ($activeFaculty->isEmpty()) {
            Notification::make()
                ->title('No Active Faculty')
                ->body('No active faculty members found for assignment')
                ->warning()
                ->send();

            return;
        }

        $facultyCount = $activeFaculty->count();
        $assignedCount = 0;

        foreach ($unassignedClasses as $index => $class) {
            $facultyIndex = $index % $facultyCount;
            $faculty = $activeFaculty[$facultyIndex];

            $class->update(['faculty_id' => (string) $faculty->id]);
            $assignedCount++;
        }

        Notification::make()
            ->title('Auto-Assignment Complete')
            ->body("Assigned {$assignedCount} classes to {$facultyCount} faculty members")
            ->success()
            ->send();

        // Refresh the page data
        $this->loadUnassignedClasses();
    }

    public function saveAssignments(): void
    {
        $assignments = $this->data['assignments'] ?? [];
        $savedCount = 0;

        foreach ($assignments as $assignment) {
            if ($assignment['class_id']) {
                Classes::where('id', $assignment['class_id'])
                    ->update(['faculty_id' => $assignment['faculty_id'] ? (string) $assignment['faculty_id'] : null]);
                $savedCount++;
            }
        }

        Notification::make()
            ->title('Assignments Saved')
            ->body("Saved {$savedCount} class assignments")
            ->success()
            ->send();
    }
}
