<?php

namespace App\Livewire;

use App\Models\StudentTuition;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class PaymentTransactionTable extends Component implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    public StudentTuition $studentTuitionRecord;

    public function mount(StudentTuition $studentTuitionRecord): void
    {
        $this->studentTuitionRecord = $studentTuitionRecord;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(StudentTransaction::query()->where('student_id', $this->studentTuitionRecord->student_id))
            ->columns([
                TextColumn::make('transaction.transaction_date')
                    ->label('Date')
                    ->date('M d, Y'),
                TextColumn::make('transaction.transaction_number')
                    ->label('Transaction No')
                    ->searchable(),
                TextColumn::make('transaction.description')
                    ->label('Description')
                    ->searchable(),
                TextColumn::make('amount')
                    ->label('Amount Paid')
                    ->money('PHP'),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        default => 'gray',
                    }),
                TextColumn::make('created_at')
                    ->label('Payment Date')
                    ->dateTime(),
                // ... other columns
            ])
            ->filters([
                // ... table filters
            ])
            ->actions([
                // ... table actions
            ])
            ->bulkActions([
                // ... table bulk actions
            ]);
    }

    public function render(): View
    {
        return view('livewire.payment-transaction-table');
    }
}
