<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GeneralSetting;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;

/**
 * @tags General Settings
 */
class GeneralSettingsApiController extends Controller
{
    /**
     * Get general school settings
     *
     * This endpoint returns the current school year, semester, and other general settings that
     * are useful for applications integrating with the school system.
     *
     * @response 200 {
     *   "data": {
     *     "school_year": "2023-2024",
     *     "school_year_string": "2023 - 2024",
     *     "semester": "1st Semester",
     *     "school_portal_url": "https://portal.example.edu",
     *     "school_portal_enabled": true,
     *     "online_enrollment_enabled": true,
     *     "features": {
     *       "enable_grades": true,
     *       "enable_enrollment": true
     *     },
     *     "curriculum_year": "2023"
     *   }
     * }
     */
    public function index(): JsonResponse
    {
        $data = Cache::remember('api_general_settings', 3600, function () {
            $settings = GeneralSetting::first();

            return [
                'school_year' => $settings->getSchoolYear(),
                'school_year_string' => $settings->getSchoolYearString(),
                'semester' => $settings->getSemester(),
                'school_portal_url' => $settings->school_portal_url,
                'school_portal_enabled' => $settings->school_portal_enabled,
                'online_enrollment_enabled' => $settings->online_enrollment_enabled,
                'features' => $settings->features,
                'curriculum_year' => $settings->curriculum_year,
            ];
        });

        return response()->json(['data' => $data]);
    }
}
