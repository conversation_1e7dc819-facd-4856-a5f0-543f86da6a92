<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\DB;

class CacheQueries
{
    public function handle($request, Closure $next)
    {
        if ($request->is('admin/students*')) {
            DB::enableQueryLog();
        }

        $response = $next($request);

        if ($request->is('admin/students*')) {
            $queries = DB::getQueryLog();
            // Cache expensive queries
            foreach ($queries as $query) {
                if (str_contains($query['sql'], 'COUNT(*)') || str_contains($query['sql'], 'GROUP BY')) {
                    $key = 'query_'.md5($query['sql'].serialize($query['bindings']));
                    cache()->remember($key, 3600, fn () => DB::select($query['sql'], $query['bindings']));
                }
            }
        }

        return $response;
    }
}
