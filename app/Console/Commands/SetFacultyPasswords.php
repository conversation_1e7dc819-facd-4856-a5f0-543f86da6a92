<?php

namespace App\Console\Commands;

use App\Models\Faculty;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class SetFacultyPasswords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'faculty:set-passwords {--default= : Default password to set for all faculty}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set passwords for faculty members who don\'t have one';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $defaultPassword = $this->option('default') ?? 'password123';

        $faculty = Faculty::whereNull('password')->get();

        if ($faculty->isEmpty()) {
            $this->info('No faculty members found without passwords.');

            return;
        }

        $bar = $this->output->createProgressBar($faculty->count());
        $bar->start();

        foreach ($faculty as $member) {
            $member->update([
                'password' => Hash::make($defaultPassword),
                'email_verified_at' => now(),
            ]);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('Successfully set passwords for '.$faculty->count().' faculty members.');
        $this->info('Default password: '.$defaultPassword);
    }
}
