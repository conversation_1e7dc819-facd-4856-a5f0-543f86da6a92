<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class VerifyDeployment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verify:deployment {--fix : Attempt to fix issues automatically}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify the deployment setup including Browsershot, environment variables, and directories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Verifying Deployment Setup...');
        $this->newLine();

        $allGood = true;
        $issues = [];

        // Check environment variables
        $allGood &= $this->checkEnvironmentVariables($issues);

        // Check binary paths
        $allGood &= $this->checkBinaryPaths($issues);

        // Check directories
        $allGood &= $this->checkDirectories($issues);

        // Check permissions
        $allGood &= $this->checkPermissions($issues);

        // Check Browsershot configuration
        $allGood &= $this->checkBrowsershotConfig($issues);

        // Test Browsershot
        $allGood &= $this->testBrowsershot($issues);

        // Check queue configuration
        $allGood &= $this->checkQueueConfig($issues);

        // Summary
        $this->newLine();
        if ($allGood) {
            $this->info('✅ All deployment checks passed!');
            $this->info('🎉 Your deployment is ready for PDF generation.');
        } else {
            $this->error('❌ Some deployment checks failed.');
            $this->newLine();
            $this->error('Issues found:');
            foreach ($issues as $issue) {
                $this->line("  • {$issue}");
            }

            if ($this->option('fix')) {
                $this->newLine();
                $this->info('🔧 Attempting to fix issues...');
                $this->fixIssues();
            } else {
                $this->newLine();
                $this->info('💡 Run with --fix to attempt automatic fixes.');
            }
        }

        return $allGood ? 0 : 1;
    }

    /**
     * Check environment variables
     */
    private function checkEnvironmentVariables(array &$issues): bool
    {
        $this->info('📋 Checking Environment Variables...');

        $requiredEnvVars = [
            'CHROME_PATH' => 'Chrome/Chromium binary path',
            'NODE_BINARY_PATH' => 'Node.js binary path',
            'BROWSERSHOT_NO_SANDBOX' => 'Browsershot no-sandbox mode',
            'BROWSERSHOT_DISABLE_WEB_SECURITY' => 'Browsershot web security disabled',
            'BROWSERSHOT_TIMEOUT' => 'Browsershot timeout setting',
        ];

        $allGood = true;

        foreach ($requiredEnvVars as $var => $description) {
            $value = env($var);
            if ($value) {
                $this->line("  ✅ {$var}: {$value}");
            } else {
                $this->line("  ❌ {$var}: not set");
                $issues[] = "Environment variable {$var} is not set ({$description})";
                $allGood = false;
            }
        }

        return $allGood;
    }

    /**
     * Check binary paths
     */
    private function checkBinaryPaths(array &$issues): bool
    {
        $this->info('🔧 Checking Binary Paths...');

        $binaries = [
            'Chrome' => env('CHROME_PATH'),
            'Node' => env('NODE_BINARY_PATH'),
        ];

        $allGood = true;

        foreach ($binaries as $name => $path) {
            if (! $path) {
                $this->line("  ❌ {$name}: path not configured");
                $issues[] = "{$name} binary path is not configured";
                $allGood = false;

                continue;
            }

            if (! file_exists($path)) {
                $this->line("  ❌ {$name}: file does not exist at {$path}");
                $issues[] = "{$name} binary does not exist at {$path}";
                $allGood = false;

                continue;
            }

            if (! is_executable($path)) {
                $this->line("  ❌ {$name}: file is not executable at {$path}");
                $issues[] = "{$name} binary is not executable at {$path}";
                $allGood = false;

                continue;
            }

            // Test the binary
            $version = $this->getBinaryVersion($name, $path);
            if ($version) {
                $this->line("  ✅ {$name}: {$version} at {$path}");
            } else {
                $this->line(
                    "  ⚠️  {$name}: exists but version check failed at {$path}"
                );
                $issues[] = "{$name} binary exists but cannot get version (may still work)";
            }
        }

        // Check fake NPM
        $npmPath = '/tmp/fake-npm-bin/npm.js';
        if (file_exists($npmPath)) {
            $this->line("  ✅ Fake NPM: exists at {$npmPath}");
        } else {
            $this->line("  ❌ Fake NPM: missing at {$npmPath}");
            $issues[] =
                'Fake NPM script is missing (deployment may not have completed properly)';
            $allGood = false;
        }

        return $allGood;
    }

    /**
     * Check required directories
     */
    private function checkDirectories(array &$issues): bool
    {
        $this->info('📁 Checking Directories...');

        $directories = [
            storage_path('app/private') => 'Private storage for PDFs',
            storage_path(
                'app/browsershot-temp'
            ) => 'Browsershot temporary files',
            '/tmp/chrome-crashpad' => 'Chrome crash dump directory',
            '/tmp/chrome-user-data' => 'Chrome user data directory',
        ];

        $allGood = true;

        foreach ($directories as $dir => $description) {
            if (is_dir($dir)) {
                $writable = is_writable($dir);
                if ($writable) {
                    $this->line("  ✅ {$dir} (writable)");
                } else {
                    $this->line("  ⚠️  {$dir} (not writable)");
                    $issues[] = "Directory {$dir} is not writable ({$description})";
                    $allGood = false;
                }
            } else {
                $this->line("  ❌ {$dir} (missing)");
                $issues[] = "Directory {$dir} does not exist ({$description})";
                $allGood = false;
            }
        }

        return $allGood;
    }

    /**
     * Check Browsershot configuration
     */
    private function checkBrowsershotConfig(array &$issues): bool
    {
        $this->info('⚙️  Checking Browsershot Configuration...');

        $config = config('browsershot', []);
        $allGood = true;

        // Check if configuration file exists
        if (empty($config)) {
            $this->line('  ❌ Browsershot configuration is empty or missing');
            $issues[] = 'Browsershot configuration file is missing or empty';

            return false;
        }

        // Check Chrome arguments
        $chromeArgs = $config['default_options']['chrome_args'] ?? [];
        $requiredArgs = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
        ];

        foreach ($requiredArgs as $arg) {
            if (in_array($arg, $chromeArgs)) {
                $this->line("  ✅ Chrome argument: {$arg}");
            } else {
                $this->line("  ❌ Missing Chrome argument: {$arg}");
                $issues[] = "Required Chrome argument {$arg} is missing from configuration";
                $allGood = false;
            }
        }

        // Check timeout
        $timeout = $config['default_options']['timeout'] ?? 60;
        if ($timeout >= 120) {
            $this->line("  ✅ Timeout: {$timeout}s");
        } else {
            $this->line(
                "  ⚠️  Timeout: {$timeout}s (recommended: 120s or higher)"
            );
            $issues[] = "Browsershot timeout is low ({$timeout}s), consider increasing to 120s+";
        }

        return $allGood;
    }

    /**
     * Test Browsershot functionality
     */
    private function testBrowsershot(array &$issues): bool
    {
        $this->info('🧪 Testing Browsershot...');

        try {
            $html =
                '<html><body><h1>Deployment Test</h1><p>Generated at: '.
                now().
                '</p></body></html>';
            $testPath = storage_path('app/deployment-test.pdf');

            $success = BrowsershotService::generatePdf($html, $testPath, [
                'format' => 'A4',
                'timeout' => 60,
                'print_background' => true,
            ]);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->line(
                    '  ✅ PDF generation successful (size: '.
                        number_format($fileSize).
                        ' bytes)'
                );

                // Clean up test file
                unlink($testPath);

                return true;
            } else {
                $this->line('  ❌ PDF generation failed: file not created');
                $issues[] = 'Browsershot PDF generation test failed';

                return false;
            }
        } catch (\Exception $e) {
            $this->line('  ❌ PDF generation failed: '.$e->getMessage());
            $issues[] = 'Browsershot test failed: '.$e->getMessage();

            return false;
        }
    }

    /**
     * Check queue configuration
     */
    private function checkQueueConfig(array &$issues): bool
    {
        $this->info('🔄 Checking Queue Configuration...');

        $allGood = true;

        // Check if Redis is available (common queue driver)
        $queueDriver = config('queue.default');
        $this->line("  📋 Queue driver: {$queueDriver}");

        // Check if pdf-generation queue exists in config
        $connections = config('queue.connections', []);
        if (isset($connections[$queueDriver])) {
            $this->line("  ✅ Queue connection configured: {$queueDriver}");
        } else {
            $this->line("  ❌ Queue connection not found: {$queueDriver}");
            $issues[] = "Queue connection {$queueDriver} is not properly configured";
            $allGood = false;
        }

        return $allGood;
    }

    /**
     * Check file and directory permissions
     */
    private function checkPermissions(array &$issues): bool
    {
        $this->info('🔐 Checking Permissions...');

        $allGood = true;

        // Check storage directory permissions
        $storagePath = storage_path();
        if (is_dir($storagePath)) {
            $perms = substr(sprintf('%o', fileperms($storagePath)), -3);
            if (in_array($perms, ['775', '755'])) {
                $this->line("  ✅ Storage directory permissions: {$perms}");
            } else {
                $this->line(
                    "  ❌ Storage directory permissions: {$perms} (should be 775 or 755)"
                );
                $issues[] = "Storage directory has incorrect permissions ({$perms})";
                $allGood = false;
            }
        }

        // Check log file specifically
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $perms = substr(sprintf('%o', fileperms($logFile)), -3);
            if (in_array($perms, ['664', '666', '644'])) {
                $this->line("  ✅ Log file permissions: {$perms}");

                // Test actual writing
                try {
                    $testMessage =
                        '['.date('Y-m-d H:i:s')."] Permission test\n";
                    file_put_contents(
                        $logFile,
                        $testMessage,
                        FILE_APPEND | LOCK_EX
                    );
                    $this->line('  ✅ Log file is writable');
                } catch (\Exception $e) {
                    $this->line(
                        '  ❌ Log file exists but is not writable: '.
                            $e->getMessage()
                    );
                    $issues[] = 'Log file is not writable';
                    $allGood = false;
                }
            } else {
                $this->line(
                    "  ❌ Log file permissions: {$perms} (should be 664, 666, or 644)"
                );
                $issues[] = "Log file has incorrect permissions ({$perms})";
                $allGood = false;
            }
        } else {
            $this->line('  ❌ Log file does not exist');
            $issues[] = 'Laravel log file does not exist';
            $allGood = false;
        }

        // Check ownership if possible
        if (function_exists('posix_getpwuid') && function_exists('fileowner')) {
            $owner = posix_getpwuid(fileowner($storagePath));
            $ownerName = $owner['name'] ?? 'unknown';

            if (in_array($ownerName, ['www-data', 'nginx', 'apache', 'root'])) {
                $this->line("  ✅ Storage owned by: {$ownerName}");
            } else {
                $this->line(
                    "  ⚠️  Storage owned by: {$ownerName} (may cause issues)"
                );
                $issues[] = "Storage directory owned by {$ownerName} (consider www-data)";
            }
        }

        return $allGood;
    }

    /**
     * Get binary version
     */
    private function getBinaryVersion(string $name, string $path): ?string
    {
        try {
            if ($name === 'Chrome') {
                $output = shell_exec(
                    "{$path} --version --no-sandbox --disable-dev-shm-usage 2>/dev/null"
                );
            } else {
                $output = shell_exec("{$path} --version 2>/dev/null");
            }

            return $output ? trim($output) : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Attempt to fix common issues
     */
    private function fixIssues(): void
    {
        $this->info('🔧 Running storage permissions fix...');

        // Run the storage permissions fix command
        try {
            $this->call('fix:storage-permissions');
            $this->info('  ✅ Storage permissions fixed');
        } catch (\Exception $e) {
            $this->error(
                '  ❌ Failed to fix storage permissions: '.$e->getMessage()
            );
        }

        // Create missing directories
        $directories = [
            storage_path('app/private'),
            storage_path('app/browsershot-temp'),
            '/tmp/chrome-crashpad',
            '/tmp/chrome-user-data',
        ];

        foreach ($directories as $dir) {
            if (! is_dir($dir)) {
                try {
                    File::makeDirectory($dir, 0755, true);
                    $this->info("  ✅ Created directory: {$dir}");
                } catch (\Exception $e) {
                    $this->error(
                        "  ❌ Failed to create directory {$dir}: ".
                            $e->getMessage()
                    );
                }
            }
        }

        // Set permissions for Chrome directories
        $chromeDirectories = ['/tmp/chrome-crashpad', '/tmp/chrome-user-data'];
        foreach ($chromeDirectories as $dir) {
            if (is_dir($dir)) {
                try {
                    chmod($dir, 0777);
                    $this->info("  ✅ Set permissions for: {$dir}");
                } catch (\Exception $e) {
                    $this->error(
                        "  ❌ Failed to set permissions for {$dir}: ".
                            $e->getMessage()
                    );
                }
            }
        }

        // Test log file writing after fixes
        try {
            $logFile = storage_path('logs/laravel.log');
            $testMessage =
                '['.date('Y-m-d H:i:s')."] Deployment verification test\n";
            file_put_contents($logFile, $testMessage, FILE_APPEND | LOCK_EX);
            $this->info('  ✅ Log file write test successful');
        } catch (\Exception $e) {
            $this->error(
                '  ❌ Log file still not writable: '.$e->getMessage()
            );
        }

        $this->info('🔧 Automatic fixes completed.');
        $this->info(
            '💡 If issues persist, check the nixpacks.toml configuration and environment variables.'
        );
    }
}
