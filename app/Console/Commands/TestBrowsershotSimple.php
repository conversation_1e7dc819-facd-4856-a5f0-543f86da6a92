<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Spatie\Browsershot\Browsershot;

class TestBrowsershotSimple extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:browsershot-simple {--direct : Test direct Browsershot instead of service}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Simple Browsershot test with minimal configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Browsershot with minimal configuration...');
        $this->newLine();

        // Show current environment
        $this->showEnvironment();

        if ($this->option('direct')) {
            $this->testDirectBrowsershot();
        } else {
            $this->testBrowsershotService();
        }

        return 0;
    }

    /**
     * Show current environment configuration
     */
    private function showEnvironment()
    {
        $this->info('Environment Configuration:');
        $this->line('========================');

        $envVars = [
            'CHROME_PATH' => env('CHROME_PATH', 'not set'),
            'NODE_BINARY_PATH' => env('NODE_BINARY_PATH', 'not set'),
            'NPM_BINARY_PATH' => env('NPM_BINARY_PATH', 'not set'),
            'BROWSERSHOT_NO_SANDBOX' => env('BROWSERSHOT_NO_SANDBOX', 'not set'),
            'BROWSERSHOT_DISABLE_WEB_SECURITY' => env('BROWSERSHOT_DISABLE_WEB_SECURITY', 'not set'),
            'BROWSERSHOT_TIMEOUT' => env('BROWSERSHOT_TIMEOUT', 'not set'),
        ];

        foreach ($envVars as $key => $value) {
            $this->line("{$key}: {$value}");
        }

        $this->newLine();

        // Check if binaries exist
        $chromePath = env('CHROME_PATH');
        if ($chromePath && file_exists($chromePath)) {
            $this->info("✅ Chrome binary exists at: {$chromePath}");

            // Test Chrome version with root-safe flags
            $output = shell_exec("{$chromePath} --version --no-sandbox --disable-dev-shm-usage 2>&1");
            $this->line('Chrome version: '.trim($output ?? 'Unable to get version'));
        } else {
            $this->error('❌ Chrome binary not found or not accessible');
        }

        $nodePath = env('NODE_BINARY_PATH');
        if ($nodePath && file_exists($nodePath)) {
            $this->info("✅ Node binary exists at: {$nodePath}");
            $output = shell_exec("{$nodePath} --version 2>&1");
            $this->line('Node version: '.trim($output ?? 'Unable to get version'));
        } else {
            $this->error('❌ Node binary not found or not accessible');
        }

        $this->newLine();
    }

    /**
     * Test using BrowsershotService
     */
    private function testBrowsershotService()
    {
        $this->info('Testing BrowsershotService...');

        try {
            $html = '<html><body><h1>Service Test</h1><p>Generated at: '.now().'</p></body></html>';
            $outputPath = storage_path('app/browsershot-service-test.pdf');

            $this->line('Generating PDF using BrowsershotService...');

            $success = BrowsershotService::generatePdf($html, $outputPath, [
                'format' => 'A4',
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 60,
            ]);

            if ($success && file_exists($outputPath)) {
                $fileSize = filesize($outputPath);
                $this->info('✅ BrowsershotService test PASSED!');
                $this->info("PDF saved to: {$outputPath}");
                $this->info('File size: '.number_format($fileSize).' bytes');

                // Clean up
                unlink($outputPath);
            } else {
                $this->error('❌ BrowsershotService test FAILED: PDF not created');
            }
        } catch (\Exception $e) {
            $this->error('❌ BrowsershotService test FAILED with exception:');
            $this->error($e->getMessage());

            // Log full exception for debugging
            Log::error('BrowsershotService Simple Test Failed', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Test using direct Browsershot
     */
    private function testDirectBrowsershot()
    {
        $this->info('Testing direct Browsershot...');

        try {
            $html = '<html><body><h1>Direct Test</h1><p>Generated at: '.now().'</p></body></html>';
            $outputPath = storage_path('app/browsershot-direct-test.pdf');

            $this->line('Configuring Browsershot with minimal settings...');

            $browsershot = Browsershot::html($html)
                ->noSandbox()
                ->disableWebSecurity()
                ->timeout(60)
                ->format('A4')
                ->printBackground()
                ->margins(10, 10, 10, 10, 'mm');

            // Set paths from environment
            if ($chromePath = env('CHROME_PATH')) {
                $browsershot->setChromePath($chromePath);
                $this->line("Using Chrome path: {$chromePath}");
            }

            if ($nodePath = env('NODE_BINARY_PATH')) {
                $browsershot->setNodeBinary($nodePath);
                $this->line("Using Node path: {$nodePath}");
            }

            // Add essential Chrome arguments for root execution
            $browsershot->addChromiumArguments([
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-zygote',
                '--single-process',
                '--disable-web-security',
                '--user-data-dir=/tmp/chrome-user-data',
                '--crash-dumps-dir=/tmp/chrome-crashpad',
                '--disable-crash-reporter',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-extensions',
                '--mute-audio',
            ]);

            $this->line('Generating PDF...');
            $browsershot->save($outputPath);

            if (file_exists($outputPath)) {
                $fileSize = filesize($outputPath);
                $this->info('✅ Direct Browsershot test PASSED!');
                $this->info("PDF saved to: {$outputPath}");
                $this->info('File size: '.number_format($fileSize).' bytes');

                // Clean up
                unlink($outputPath);
            } else {
                $this->error('❌ Direct Browsershot test FAILED: PDF not created');
            }
        } catch (\Exception $e) {
            $this->error('❌ Direct Browsershot test FAILED with exception:');
            $this->error($e->getMessage());

            // Log full exception for debugging
            Log::error('Direct Browsershot Simple Test Failed', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
