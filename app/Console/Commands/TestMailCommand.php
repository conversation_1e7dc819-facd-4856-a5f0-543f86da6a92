<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Mailer\Mailer;
use Symfony\Component\Mailer\Transport\Smtp\EsmtpTransport;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

class TestMailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:test {email? : The email address to send the test mail to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email to verify SMTP configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?: '<EMAIL>';

        $this->info("Sending test email to: {$email}");

        // Get mail configuration
        $host = config('mail.mailers.smtp.host');
        $port = config('mail.mailers.smtp.port');
        $username = config('mail.mailers.smtp.username');
        $password = config('mail.mailers.smtp.password');
        $encryption = config('mail.mailers.smtp.encryption', 'tls');
        $fromAddress = config('mail.from.address');
        $fromName = config('mail.from.name');

        $this->info('Using SMTP configuration:');
        $this->info("- MAIL_HOST: {$host}");
        $this->info("- MAIL_PORT: {$port}");
        $this->info("- MAIL_USERNAME: {$username}");
        $this->info("- MAIL_ENCRYPTION: {$encryption}");
        $this->info("- MAIL_FROM_ADDRESS: {$fromAddress}");
        $this->info("- MAIL_FROM_NAME: {$fromName}");

        try {
            // Create the Transport
            $transport = new EsmtpTransport($host, $port);
            $transport->setUsername($username);
            $transport->setPassword($password);

            // Set encryption if needed
            if ($encryption === 'ssl') {
                // For SSL, we need to use the correct port and set TLS mode
                $transport = new EsmtpTransport($host, $port, true);
                $transport->setUsername($username);
                $transport->setPassword($password);
            }

            // Create the Mailer using the created Transport
            $mailer = new Mailer($transport);

            // Create a message
            $message = (new Email)
                ->from(new Address($fromAddress, $fromName))
                ->to(new Address($email))
                ->subject('DCCP Admin - SMTP Test Email')
                ->text('This is a test email to verify SMTP configuration is working correctly.')
                ->html('<p>This is a test email to verify SMTP configuration is working correctly.</p>');

            // Send the message
            $mailer->send($message);

            $this->info("Test email sent successfully! Please check {$email} inbox.");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to send test email: '.$e->getMessage());

            return Command::FAILURE;
        }
    }
}
