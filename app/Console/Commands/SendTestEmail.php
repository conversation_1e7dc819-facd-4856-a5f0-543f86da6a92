<?php

namespace App\Console\Commands;

use App\Mail\TestMail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendTestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {recipient? : Email address to send the test to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a test email using Laravel Mail';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $recipient = $this->argument('recipient') ?: '<EMAIL>';

        $this->info("Sending test email to: {$recipient}");
        $this->info('Using mail configuration:');
        $this->info('- MAIL_MAILER: '.config('mail.default'));
        $this->info('- MAIL_HOST: '.config('mail.mailers.smtp.host'));
        $this->info('- MAIL_PORT: '.config('mail.mailers.smtp.port'));
        $this->info('- MAIL_FROM_ADDRESS: '.config('mail.from.address'));

        try {
            Mail::to($recipient)->send(new TestMail);
            $this->info("Test email sent successfully! Please check {$recipient} inbox.");

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to send test email: '.$e->getMessage());
            $this->error('Stack trace: '.$e->getTraceAsString());

            return Command::FAILURE;
        }
    }
}
