<?php

namespace App\Console\Commands;

use App\Models\GeneralSetting;
use App\Models\Student;
use App\Models\StudentClearance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateStudentClearances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'students:generate-clearances {--force : Force regeneration even if clearances exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate clearance records for all students for the current semester';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $settings = GeneralSetting::first();

        if (! $settings) {
            $this->error('General settings not found. Please configure the system first.');

            return Command::FAILURE;
        }

        $academicYear = $settings->getSchoolYear();
        $semester = $settings->semester;

        $this->info("Generating clearance records for {$settings->getSemester()} of {$settings->getSchoolYearString()}");

        $force = $this->option('force');

        // Count students without clearance records for current semester/year
        $studentsQuery = Student::query();

        if (! $force) {
            $studentsQuery->whereDoesntHave('clearances', function ($query) use ($academicYear, $semester): void {
                $query->where('academic_year', $academicYear)
                    ->where('semester', $semester);
            });
        }

        $totalStudents = $studentsQuery->count();

        if ($totalStudents === 0) {
            $this->info('No students found needing clearance records.');

            return Command::SUCCESS;
        }

        $this->info("Found {$totalStudents} students that need clearance records.");

        if (! $force && $this->confirm('Do you want to continue?', true)) {
            $bar = $this->output->createProgressBar($totalStudents);
            $bar->start();

            DB::beginTransaction();

            try {
                $studentsQuery->chunk(100, function ($students) use ($bar, $academicYear, $semester, $force): void {
                    foreach ($students as $student) {
                        if ($force) {
                            // Delete existing clearance if force option is used
                            StudentClearance::where('student_id', $student->id)
                                ->where('academic_year', $academicYear)
                                ->where('semester', $semester)
                                ->delete();
                        }

                        // Create new clearance record
                        StudentClearance::create([
                            'student_id' => $student->id,
                            'academic_year' => $academicYear,
                            'semester' => $semester,
                            'is_cleared' => false,
                        ]);

                        $bar->advance();
                    }
                });

                DB::commit();
                $bar->finish();

                $this->newLine();
                $this->info('Student clearance records have been generated successfully.');

                return Command::SUCCESS;
            } catch (\Exception $e) {
                DB::rollBack();
                $this->newLine();
                $this->error('Error generating clearance records: '.$e->getMessage());

                return Command::FAILURE;
            }
        } elseif ($force) {
            $this->info('Forced clearance generation in progress...');

            // Similar process as above but without confirmation
            $bar = $this->output->createProgressBar($totalStudents);
            $bar->start();

            DB::beginTransaction();

            try {
                $studentsQuery->chunk(100, function ($students) use ($bar, $academicYear, $semester): void {
                    foreach ($students as $student) {
                        // Delete existing clearance
                        StudentClearance::where('student_id', $student->id)
                            ->where('academic_year', $academicYear)
                            ->where('semester', $semester)
                            ->delete();

                        // Create new clearance record
                        StudentClearance::create([
                            'student_id' => $student->id,
                            'academic_year' => $academicYear,
                            'semester' => $semester,
                            'is_cleared' => false,
                        ]);

                        $bar->advance();
                    }
                });

                DB::commit();
                $bar->finish();

                $this->newLine();
                $this->info('Student clearance records have been forcefully regenerated.');

                return Command::SUCCESS;
            } catch (\Exception $e) {
                DB::rollBack();
                $this->newLine();
                $this->error('Error generating clearance records: '.$e->getMessage());

                return Command::FAILURE;
            }
        }

        $this->info('Operation cancelled by user.');

        return Command::SUCCESS;
    }
}
