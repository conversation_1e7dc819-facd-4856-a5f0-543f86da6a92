<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MonitorAssessmentJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:assessment-jobs {--recent : Show recent notifications} {--logs : Show recent log entries}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor assessment job notifications and logs';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Assessment Job Monitor');
        $this->line(str_repeat('=', 50));

        if ($this->option('recent')) {
            $this->showRecentNotifications();
        } elseif ($this->option('logs')) {
            $this->showRecentLogs();
        } else {
            $this->showOverview();
        }

        return 0;
    }

    /**
     * Show overview of assessment job system
     */
    private function showOverview(): void
    {
        $this->info('Assessment Job System Overview');
        $this->newLine();

        // Show queue statistics
        try {
            $queuedJobs = DB::table('jobs')
                ->where('queue', 'assessments')
                ->count();
            $failedJobs = DB::table('failed_jobs')->count();

            $this->table(
                ['Metric', 'Count'],
                [
                    ['Queued Assessment Jobs', $queuedJobs],
                    ['Failed Jobs (Total)', $failedJobs],
                ]
            );
        } catch (\Exception $e) {
            $this->warn(
                'Could not fetch queue statistics: '.$e->getMessage()
            );
        }

        $this->newLine();
        $this->info('Available options:');
        $this->info('  --recent    Show recent assessment notifications');
        $this->info(
            '  --logs      Show recent log entries (requires log access)'
        );
    }

    /**
     * Show recent assessment notifications
     */
    private function showRecentNotifications(): void
    {
        $this->info('Recent Assessment Notifications');
        $this->newLine();

        try {
            $superAdmins = User::role('super_admin')->get();

            if ($superAdmins->isEmpty()) {
                $this->warn('No super admin users found.');

                return;
            }

            foreach ($superAdmins as $admin) {
                $notifications = $admin
                    ->notifications()
                    ->where(function ($query): void {
                        $query
                            ->where('data->title', 'like', '%Assessment%')
                            ->orWhere('data->body', 'like', '%assessment%');
                    })
                    ->latest()
                    ->take(5)
                    ->get();

                if ($notifications->isNotEmpty()) {
                    $this->info("Admin: {$admin->name}");

                    $tableData = [];
                    foreach ($notifications as $notif) {
                        $data = $notif->data;
                        $tableData[] = [
                            $notif->created_at->format('Y-m-d H:i:s'),
                            $data['title'] ?? 'No title',
                            \Illuminate\Support\Str::limit(
                                $data['body'] ?? 'No body',
                                50
                            ),
                            $notif->read_at ? 'Read' : 'Unread',
                        ];
                    }

                    $this->table(
                        ['Time', 'Title', 'Message', 'Status'],
                        $tableData
                    );
                    $this->newLine();
                }
            }
        } catch (\Exception $e) {
            $this->error('Error fetching notifications: '.$e->getMessage());
        }
    }

    /**
     * Show recent log entries
     */
    private function showRecentLogs(): void
    {
        $this->info('Recent Assessment Job Logs');
        $this->newLine();

        $logFile = storage_path('logs/laravel.log');

        if (! file_exists($logFile)) {
            $this->error('Log file not found at: '.$logFile);

            return;
        }

        try {
            $command =
                "grep -i 'assessment.*job' ".
                escapeshellarg($logFile).
                ' | tail -20';
            $output = shell_exec($command);

            if ($output) {
                $lines = explode("\n", trim($output));
                foreach ($lines as $line) {
                    if (trim($line)) {
                        $this->line($line);
                    }
                }
            } else {
                $this->info('No recent assessment job logs found.');
            }
        } catch (\Exception $e) {
            $this->error('Error reading log file: '.$e->getMessage());
        }
    }
}
