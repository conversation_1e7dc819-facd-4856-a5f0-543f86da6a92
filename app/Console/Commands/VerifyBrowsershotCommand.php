<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class VerifyBrowsershotCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verify:browsershot {--detailed : Show detailed information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify that Browsershot is properly configured and working';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Browsershot Configuration Verification');
        $this->info('==========================================');
        $this->newLine();

        $allChecks = [
            'binaries' => $this->checkBinaries(),
            'environment' => $this->checkEnvironment(),
            'laravel' => $this->checkLaravel(),
            'browsershot' => $this->checkBrowsershot(),
            'permissions' => $this->checkPermissions(),
            'dependencies' => $this->checkDependencies(),
        ];

        $this->newLine();
        $this->displaySummary($allChecks);

        // Return appropriate exit code
        $allPassed = array_reduce($allChecks, function ($carry, $check) {
            return $carry && $check['passed'];
        }, true);

        return $allPassed ? 0 : 1;
    }

    /**
     * Check if required binaries are available.
     */
    private function checkBinaries(): array
    {
        $this->info('📦 Checking Required Binaries');
        $this->line('==============================');

        $checks = [];
        $binaries = [
            'php' => 'PHP',
            'node' => 'Node.js',
            'npm' => 'NPM',
        ];

        foreach ($binaries as $binary => $name) {
            $path = shell_exec("which $binary 2>/dev/null");
            if ($path) {
                $path = trim($path);
                $version = $this->getBinaryVersion($binary);
                $this->line("  ✅ $name: $path".($version ? " ($version)" : ''));
                $checks[$binary] = ['passed' => true, 'path' => $path, 'version' => $version];
            } else {
                $this->line("  ❌ $name: Not found");
                $checks[$binary] = ['passed' => false, 'path' => null, 'version' => null];
            }
        }

        // Check Chrome/Chromium
        $chromeFound = false;
        $chromeBinaries = ['chromium', 'chromium-browser', 'google-chrome-stable', 'google-chrome'];

        foreach ($chromeBinaries as $binary) {
            $path = shell_exec("which $binary 2>/dev/null");
            if ($path) {
                $path = trim($path);
                $this->line("  ✅ Chrome/Chromium: $path");
                $checks['chrome'] = ['passed' => true, 'path' => $path, 'binary' => $binary];
                $chromeFound = true;
                break;
            }
        }

        if (! $chromeFound) {
            $this->line('  ❌ Chrome/Chromium: Not found');
            $checks['chrome'] = ['passed' => false, 'path' => null, 'binary' => null];
        }

        $this->newLine();

        return ['passed' => $chromeFound && $checks['php']['passed'] && $checks['node']['passed'] && $checks['npm']['passed'], 'details' => $checks];
    }

    /**
     * Check environment variables.
     */
    private function checkEnvironment(): array
    {
        $this->info('🌍 Checking Environment Variables');
        $this->line('=================================');

        $envVars = [
            'CHROME_PATH' => 'Chrome binary path',
            'NODE_BINARY_PATH' => 'Node.js binary path',
            'NPM_BINARY_PATH' => 'NPM binary path',
        ];

        $checks = [];
        foreach ($envVars as $var => $description) {
            $value = env($var);
            if ($value) {
                $exists = file_exists($value) ? '✅' : '⚠️ ';
                $this->line("  $exists $description: $value");
                $checks[$var] = ['passed' => file_exists($value), 'value' => $value];
            } else {
                $this->line("  ⚠️  $description: Not set");
                $checks[$var] = ['passed' => false, 'value' => null];
            }
        }

        $this->newLine();

        return ['passed' => true, 'details' => $checks]; // Environment vars are optional
    }

    /**
     * Check Laravel application.
     */
    private function checkLaravel(): array
    {
        $this->info('🏗️  Checking Laravel Application');
        $this->line('==============================');

        $checks = [];

        // Check if we can get Laravel version
        try {
            $version = app()->version();
            $this->line("  ✅ Laravel Framework: $version");
            $checks['version'] = ['passed' => true, 'version' => $version];
        } catch (\Exception $e) {
            $this->line('  ❌ Laravel Framework: Cannot determine version');
            $checks['version'] = ['passed' => false, 'error' => $e->getMessage()];
        }

        // Check configuration
        try {
            $config = config('browsershot');
            $this->line('  ✅ Browsershot configuration: Loaded');
            $checks['config'] = ['passed' => true, 'config' => $config];
        } catch (\Exception $e) {
            $this->line('  ❌ Browsershot configuration: Failed to load');
            $checks['config'] = ['passed' => false, 'error' => $e->getMessage()];
        }

        $this->newLine();

        return ['passed' => $checks['version']['passed'] && $checks['config']['passed'], 'details' => $checks];
    }

    /**
     * Check Browsershot functionality.
     */
    private function checkBrowsershot(): array
    {
        $this->info('🖨️  Checking Browsershot Functionality');
        $this->line('====================================');

        try {
            $result = BrowsershotService::test();

            if ($result['success']) {
                $this->line('  ✅ PDF Generation: Working');
                $this->line("  ✅ Chrome Path: {$result['chrome_path']}");
                $this->line("  ✅ Node Path: {$result['node_path']}");
                $this->line("  ✅ NPM Path: {$result['npm_path']}");

                if ($this->option('detailed')) {
                    $this->line('  ℹ️  Test PDF Created: '.($result['test_pdf_created'] ? 'Yes' : 'No'));
                }
            } else {
                $this->line('  ❌ PDF Generation: Failed');
                foreach ($result['errors'] as $error) {
                    $this->line("    • $error");
                }
            }

            $this->newLine();

            return ['passed' => $result['success'], 'details' => $result];

        } catch (\Exception $e) {
            $this->line('  ❌ Browsershot Test: Exception occurred');
            $this->line("    • {$e->getMessage()}");
            $this->newLine();

            return ['passed' => false, 'details' => ['error' => $e->getMessage()]];
        }
    }

    /**
     * Check file permissions and directories.
     */
    private function checkPermissions(): array
    {
        $this->info('🔐 Checking Permissions and Directories');
        $this->line('=======================================');

        $checks = [];

        // Check storage directory
        $storageDir = storage_path();
        if (File::isDirectory($storageDir) && File::isWritable($storageDir)) {
            $this->line('  ✅ Storage directory: Writable');
            $checks['storage'] = ['passed' => true, 'path' => $storageDir];
        } else {
            $this->line('  ❌ Storage directory: Not writable');
            $checks['storage'] = ['passed' => false, 'path' => $storageDir];
        }

        // Check temp directory
        $tempDir = config('browsershot.temp_directory', storage_path('app/browsershot-temp'));
        try {
            if (! File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            if (File::isDirectory($tempDir) && File::isWritable($tempDir)) {
                $this->line('  ✅ Browsershot temp directory: Writable');
                $checks['temp'] = ['passed' => true, 'path' => $tempDir];
            } else {
                $this->line('  ❌ Browsershot temp directory: Not writable');
                $checks['temp'] = ['passed' => false, 'path' => $tempDir];
            }
        } catch (\Exception $e) {
            $this->line('  ❌ Browsershot temp directory: Cannot create');
            $checks['temp'] = ['passed' => false, 'error' => $e->getMessage()];
        }

        $this->newLine();

        return ['passed' => $checks['storage']['passed'] && $checks['temp']['passed'], 'details' => $checks];
    }

    /**
     * Check dependencies.
     */
    private function checkDependencies(): array
    {
        $this->info('📚 Checking Dependencies');
        $this->line('========================');

        $checks = [];

        // Check Browsershot package
        try {
            if (class_exists('Spatie\Browsershot\Browsershot')) {
                $this->line('  ✅ Browsershot package: Installed and loadable');
                $checks['browsershot'] = ['passed' => true];
            } else {
                $this->line('  ❌ Browsershot package: Not found');
                $checks['browsershot'] = ['passed' => false];
            }
        } catch (\Exception $e) {
            $this->line('  ❌ Browsershot package: Error loading');
            $checks['browsershot'] = ['passed' => false, 'error' => $e->getMessage()];
        }

        // Check our custom service
        try {
            if (class_exists('App\Services\BrowsershotService')) {
                $this->line('  ✅ BrowsershotService: Available');
                $checks['service'] = ['passed' => true];
            } else {
                $this->line('  ❌ BrowsershotService: Not found');
                $checks['service'] = ['passed' => false];
            }
        } catch (\Exception $e) {
            $this->line('  ❌ BrowsershotService: Error loading');
            $checks['service'] = ['passed' => false, 'error' => $e->getMessage()];
        }

        $this->newLine();

        return ['passed' => $checks['browsershot']['passed'] && $checks['service']['passed'], 'details' => $checks];
    }

    /**
     * Display verification summary.
     */
    private function displaySummary(array $checks): void
    {
        $this->info('📋 Verification Summary');
        $this->line('=======================');

        $passed = 0;
        $total = count($checks);

        foreach ($checks as $category => $result) {
            $status = $result['passed'] ? '✅' : '❌';
            $name = ucfirst(str_replace('_', ' ', $category));
            $this->line("  $status $name");

            if ($result['passed']) {
                $passed++;
            }
        }

        $this->newLine();

        if ($passed === $total) {
            $this->info("🎉 All checks passed! Browsershot is ready for production. ($passed/$total)");
        } else {
            $this->error("⚠️  Some checks failed. Please review the issues above. ($passed/$total)");

            if ($this->option('detailed')) {
                $this->newLine();
                $this->warn('Troubleshooting Tips:');
                $this->line('• Make sure Chrome/Chromium is installed and accessible');
                $this->line('• Verify Node.js and NPM are installed');
                $this->line('• Check file permissions on storage directories');
                $this->line('• Ensure all required packages are installed via Composer');
                $this->line('• Review the BROWSERSHOT_CONFIG.md file for more details');
            }
        }
    }

    /**
     * Get version information for a binary.
     */
    private function getBinaryVersion(string $binary): ?string
    {
        switch ($binary) {
            case 'php':
                return PHP_VERSION;
            case 'node':
                $version = shell_exec('node --version 2>/dev/null');

                return $version ? trim($version) : null;
            case 'npm':
                $version = shell_exec('npm --version 2>/dev/null');

                return $version ? 'v'.trim($version) : null;
            default:
                return null;
        }
    }
}
