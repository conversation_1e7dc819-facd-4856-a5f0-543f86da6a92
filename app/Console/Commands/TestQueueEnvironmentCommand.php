<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class TestQueueEnvironmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:queue-environment {--sync : Run job synchronously instead of queuing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test queue worker environment for browsershot paths';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Queue Worker Environment for Browsershot');
        $this->line('=============================================');
        $this->newLine();

        if ($this->option('sync')) {
            $this->testSynchronousExecution();
        } else {
            $this->testQueuedExecution();
        }
    }

    /**
     * Test synchronous execution (current process)
     */
    private function testSynchronousExecution()
    {
        $this->info('Testing Synchronous Execution (Current Process):');
        $this->line('-----------------------------------------------');

        $this->runEnvironmentTest('sync');
    }

    /**
     * Test queued execution (worker process)
     */
    private function testQueuedExecution()
    {
        $this->info('Testing Queued Execution (Worker Process):');
        $this->line('------------------------------------------');

        $this->info('Dispatching test job to queue...');

        // Dispatch a test job
        $jobId = uniqid('test_queue_', true);

        Queue::push(new \App\Jobs\TestBrowsershotEnvironmentJob($jobId));

        $this->info("Test job dispatched with ID: {$jobId}");
        $this->newLine();
        $this->info('Check the queue worker logs and application logs for results:');
        $this->line('- Queue worker: Check supervisor logs or console output');
        $this->line('- App logs: tail -f storage/logs/laravel.log | grep "'.$jobId.'"');
        $this->newLine();

        $this->warn('Make sure your queue worker is running:');
        $this->line('php artisan queue:work --timeout=300');
    }

    /**
     * Run environment test
     */
    private function runEnvironmentTest($context)
    {
        $testId = uniqid('env_test_', true);

        $this->line("Test ID: {$testId}");
        $this->newLine();

        // Test 1: Environment Variables
        $this->testEnvironmentVariables($testId, $context);

        // Test 2: BrowsershotService Path Detection
        $this->testBrowsershotPaths($testId, $context);

        // Test 3: File Accessibility
        $this->testFileAccessibility($testId, $context);

        // Test 4: Simple PDF Generation
        $this->testSimplePdfGeneration($testId, $context);

        $this->newLine();
        $this->info("Environment test completed for {$context} execution");
    }

    /**
     * Test environment variables
     */
    private function testEnvironmentVariables($testId, $context)
    {
        $this->info('1. Environment Variables:');

        $envVars = [
            'CHROME_PATH',
            'NODE_BINARY_PATH',
            'NPM_BINARY_PATH',
            'BROWSERSHOT_NO_SANDBOX',
            'BROWSERSHOT_TIMEOUT',
            'BROWSERSHOT_TEMP_DIRECTORY',
            'QUEUE_CONNECTION',
        ];

        $results = [];
        foreach ($envVars as $var) {
            $value = env($var);
            $results[$var] = $value ?: 'NOT_SET';
            $status = $value ? '✅' : '❌';
            $this->line("   {$var}: {$status} ".($value ?: 'Not set'));
        }

        Log::info('Environment test - Variables', [
            'test_id' => $testId,
            'context' => $context,
            'environment_variables' => $results,
            'php_version' => PHP_VERSION,
            'process_id' => getmypid(),
        ]);

        $this->newLine();
    }

    /**
     * Test BrowsershotService path detection
     */
    private function testBrowsershotPaths($testId, $context)
    {
        $this->info('2. BrowsershotService Path Detection:');

        try {
            $config = config('browsershot', []);

            // Use reflection to access private methods
            $reflection = new \ReflectionClass(BrowsershotService::class);

            $detectChrome = $reflection->getMethod('detectChromePath');
            $detectChrome->setAccessible(true);
            $chromePath = $detectChrome->invoke(null, $config);

            $detectNode = $reflection->getMethod('detectNodePath');
            $detectNode->setAccessible(true);
            $nodePath = $detectNode->invoke(null, $config);

            $detectNpm = $reflection->getMethod('detectNpmPath');
            $detectNpm->setAccessible(true);
            $npmPath = $detectNpm->invoke(null, $config);

            $results = [
                'chrome_path' => $chromePath ?: 'NOT_DETECTED',
                'node_path' => $nodePath ?: 'NOT_DETECTED',
                'npm_path' => $npmPath ?: 'NOT_DETECTED',
            ];

            foreach ($results as $key => $path) {
                $status = $path !== 'NOT_DETECTED' ? '✅' : '❌';
                $this->line("   {$key}: {$status} {$path}");
            }

            Log::info('Environment test - Path Detection', [
                'test_id' => $testId,
                'context' => $context,
                'detected_paths' => $results,
                'config' => $config,
            ]);

        } catch (\Exception $e) {
            $this->error("   Failed to detect paths: {$e->getMessage()}");

            Log::error('Environment test - Path Detection Failed', [
                'test_id' => $testId,
                'context' => $context,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        $this->newLine();
    }

    /**
     * Test file accessibility
     */
    private function testFileAccessibility($testId, $context)
    {
        $this->info('3. Binary File Accessibility:');

        $paths = [
            'chromium' => [
                '/root/.nix-profile/bin/chromium',
                '/nix/var/nix/profiles/default/bin/chromium',
                '/sbin/chromium',
                '/usr/bin/chromium',
                '/usr/bin/chromium-browser',
            ],
            'node' => [
                '/root/.nix-profile/bin/node',
                '/nix/var/nix/profiles/default/bin/node',
                '/sbin/node',
                '/usr/bin/node',
            ],
            'npm' => [
                '/root/.nix-profile/bin/npm',
                '/nix/var/nix/profiles/default/bin/npm',
                '/sbin/npm',
                '/usr/bin/npm',
            ],
        ];

        $accessible = [];
        foreach ($paths as $binary => $pathList) {
            $this->line("   {$binary}:");
            $found = false;

            foreach ($pathList as $path) {
                $exists = file_exists($path);
                $executable = $exists && is_executable($path);

                if ($executable) {
                    $accessible[$binary][] = $path;
                    $found = true;
                }

                $status = $executable ? '✅' : ($exists ? '⚠️' : '❌');
                $note = $executable ? 'OK' : ($exists ? 'Not executable' : 'Not found');
                $this->line("     {$path}: {$status} {$note}");
            }

            if (! $found) {
                $this->line("     No accessible {$binary} binary found!");
            }
        }

        Log::info('Environment test - File Accessibility', [
            'test_id' => $testId,
            'context' => $context,
            'accessible_binaries' => $accessible,
            'all_paths_tested' => $paths,
        ]);

        $this->newLine();
    }

    /**
     * Test simple PDF generation
     */
    private function testSimplePdfGeneration($testId, $context)
    {
        $this->info('4. Simple PDF Generation Test:');

        try {
            $html = '
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Queue Environment Test</title>
                </head>
                <body>
                    <h1>Queue Environment Test</h1>
                    <p>Test ID: '.$testId.'</p>
                    <p>Context: '.$context.'</p>
                    <p>Generated at: '.now()->format('Y-m-d H:i:s').'</p>
                    <p>Process ID: '.getmypid().'</p>
                </body>
                </html>
            ';

            $testPath = storage_path("app/queue-env-test-{$testId}.pdf");

            $options = [
                'format' => 'A4',
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'print_background' => true,
                'timeout' => 60,
            ];

            $this->line('   Attempting PDF generation...');

            $success = BrowsershotService::generatePdf($html, $testPath, $options);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->info('   ✅ PDF generated successfully!');
                $this->line("   Path: {$testPath}");
                $this->line('   Size: '.number_format($fileSize).' bytes');

                Log::info('Environment test - PDF Generation Success', [
                    'test_id' => $testId,
                    'context' => $context,
                    'file_path' => $testPath,
                    'file_size' => $fileSize,
                    'options' => $options,
                ]);

                // Clean up test file
                unlink($testPath);
                $this->line('   Test file cleaned up');

            } else {
                $this->error('   ❌ PDF generation failed');
                $this->line('   Success flag: '.($success ? 'true' : 'false'));
                $this->line('   File exists: '.(file_exists($testPath) ? 'true' : 'false'));

                Log::error('Environment test - PDF Generation Failed', [
                    'test_id' => $testId,
                    'context' => $context,
                    'success_flag' => $success,
                    'file_exists' => file_exists($testPath),
                    'expected_path' => $testPath,
                    'options' => $options,
                ]);
            }

        } catch (\Exception $e) {
            $this->error('   ❌ Exception during PDF generation:');
            $this->line("   {$e->getMessage()}");

            Log::error('Environment test - PDF Generation Exception', [
                'test_id' => $testId,
                'context' => $context,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
