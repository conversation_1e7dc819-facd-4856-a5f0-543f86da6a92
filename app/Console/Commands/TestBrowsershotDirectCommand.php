<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Spatie\Browsershot\Browsershot;

class TestBrowsershotDirectCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:browsershot-direct';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Browsershot directly with system Chromium to bypass Puppeteer issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Direct Browsershot Test (Bypassing Puppeteer)');
        $this->info('=================================================');
        $this->newLine();

        // Detect system paths
        $chromePath = $this->detectChromePath();
        $nodePath = $this->detectNodePath();
        $npmPath = $this->detectNpmPath();

        $this->info('Detected paths:');
        $this->line('  Chrome: '.($chromePath ?: 'NOT FOUND'));
        $this->line('  Node: '.($nodePath ?: 'NOT FOUND'));
        $this->line('  NPM: '.($npmPath ?: 'NOT FOUND'));
        $this->newLine();

        if (! $chromePath) {
            $this->error('Chrome/Chromium not found. Cannot proceed with test.');

            return 1;
        }

        if (! $nodePath) {
            $this->error('Node.js not found. Cannot proceed with test.');

            return 1;
        }

        // Test HTML content
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <title>Browsershot Test</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #333; }
                .info { background: #f0f0f0; padding: 20px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <h1>Direct Browsershot Test</h1>
            <div class="info">
                <p>This PDF was generated using system Chromium:</p>
                <p><strong>Chrome Path:</strong> '.$chromePath.'</p>
                <p><strong>Node Path:</strong> '.$nodePath.'</p>
                <p><strong>Generated:</strong> '.now()->toDateTimeString().'</p>
            </div>
        </body>
        </html>';

        $outputPath = storage_path('app/browsershot_direct_test.pdf');
        $tempDir = storage_path('app/browsershot-direct-temp');

        // Ensure temp directory exists
        if (! File::exists($tempDir)) {
            File::makeDirectory($tempDir, 0755, true);
        }

        try {
            $this->info('Configuring Browsershot with system binaries...');

            $browsershot = Browsershot::html($html)
                ->setChromePath($chromePath)
                ->noSandbox()
                ->disableWebSecurity()
                ->setTemporaryDirectory($tempDir)
                ->timeout(120)
                ->format('A4')
                ->margins(10, 10, 10, 10)
                ->printBackground();

            // Set Node and NPM paths if available
            if ($nodePath) {
                $browsershot->setNodeBinary($nodePath);
            }
            if ($npmPath) {
                $browsershot->setNpmBinary($npmPath);
            }

            $this->info('Generating PDF...');
            $browsershot->save($outputPath);

            if (file_exists($outputPath)) {
                $fileSize = File::size($outputPath);
                $this->info('✅ SUCCESS! PDF generated successfully');
                $this->info("   File: {$outputPath}");
                $this->info('   Size: '.number_format($fileSize).' bytes');
                $this->newLine();

                // Test with different options
                $this->info('Testing with different Chrome arguments...');
                $this->testDifferentChromeArgs($html, $chromePath, $nodePath, $npmPath, $tempDir);

            } else {
                $this->error('❌ FAILED! PDF file was not created');

                return 1;
            }

        } catch (\Exception $e) {
            $this->error('❌ FAILED! Exception occurred:');
            $this->error("   {$e->getMessage()}");
            $this->newLine();

            // Try to provide helpful debugging info
            $this->info('Debugging information:');
            $this->testChromeDirectly($chromePath);
            $this->testNodeDirectly($nodePath);

            return 1;

        } finally {
            // Clean up temp directory
            if (File::isDirectory($tempDir)) {
                File::deleteDirectory($tempDir);
            }
        }

        return 0;
    }

    /**
     * Detect Chrome/Chromium path
     */
    private function detectChromePath(): ?string
    {
        $paths = [
            '/root/.nix-profile/bin/chromium',
            '/root/.nix-profile/bin/chromium-browser',
            '/nix/var/nix/profiles/default/bin/chromium',
            '/nix/var/nix/profiles/default/bin/chromium-browser',
        ];

        foreach ($paths as $path) {
            if (file_exists($path) && is_executable($path)) {
                return $path;
            }
        }

        // Fallback to which
        $which = trim(shell_exec('which chromium 2>/dev/null') ?: '');
        if ($which && file_exists($which) && is_executable($which)) {
            return $which;
        }

        return null;
    }

    /**
     * Detect Node.js path
     */
    private function detectNodePath(): ?string
    {
        $paths = [
            '/root/.nix-profile/bin/node',
            '/nix/var/nix/profiles/default/bin/node',
        ];

        foreach ($paths as $path) {
            if (file_exists($path) && is_executable($path)) {
                return $path;
            }
        }

        $which = trim(shell_exec('which node 2>/dev/null') ?: '');
        if ($which && file_exists($which) && is_executable($which)) {
            return $which;
        }

        return null;
    }

    /**
     * Detect NPM path
     */
    private function detectNpmPath(): ?string
    {
        $paths = [
            '/root/.nix-profile/bin/npm',
            '/nix/var/nix/profiles/default/bin/npm',
        ];

        foreach ($paths as $path) {
            if (file_exists($path) && is_executable($path)) {
                return $path;
            }
        }

        $which = trim(shell_exec('which npm 2>/dev/null') ?: '');
        if ($which && file_exists($which) && is_executable($which)) {
            return $which;
        }

        return null;
    }

    /**
     * Test different Chrome arguments
     */
    private function testDifferentChromeArgs($html, $chromePath, $nodePath, $npmPath, $tempDir)
    {
        $testCases = [
            'minimal' => ['--no-sandbox', '--headless', '--disable-gpu'],
            'safe' => ['--no-sandbox', '--headless', '--disable-gpu', '--disable-web-security', '--disable-features=VizDisplayCompositor'],
            'extended' => ['--no-sandbox', '--headless', '--disable-gpu', '--disable-web-security', '--disable-features=VizDisplayCompositor', '--disable-background-timer-throttling', '--disable-backgrounding-occluded-windows', '--disable-renderer-backgrounding'],
        ];

        foreach ($testCases as $name => $args) {
            try {
                $testPath = storage_path("app/browsershot_test_{$name}.pdf");

                $browsershot = Browsershot::html("<h1>Test: {$name}</h1><p>Args: ".implode(' ', $args).'</p>')
                    ->setChromePath($chromePath)
                    ->setTemporaryDirectory($tempDir)
                    ->timeout(60);

                if ($nodePath) {
                    $browsershot->setNodeBinary($nodePath);
                }
                if ($npmPath) {
                    $browsershot->setNpmBinary($npmPath);
                }

                // Add custom Chrome arguments
                foreach ($args as $arg) {
                    $browsershot->addChromiumArguments([$arg]);
                }

                $browsershot->save($testPath);

                if (file_exists($testPath)) {
                    $size = File::size($testPath);
                    $this->line("  ✅ {$name}: ".number_format($size).' bytes');
                    unlink($testPath); // Clean up
                } else {
                    $this->line("  ❌ {$name}: Failed");
                }

            } catch (\Exception $e) {
                $this->line("  ❌ {$name}: {$e->getMessage()}");
            }
        }
    }

    /**
     * Test Chrome directly
     */
    private function testChromeDirectly($chromePath)
    {
        if (! $chromePath) {
            return;
        }

        $this->line('Testing Chrome binary directly:');

        // Test version
        $version = shell_exec("{$chromePath} --version 2>&1");
        $this->line('  Version: '.trim($version ?: 'Unable to get version'));

        // Test basic headless operation
        $test = shell_exec("{$chromePath} --headless --disable-gpu --no-sandbox --dump-dom 'data:text/html,<h1>Test</h1>' 2>&1");
        if (strpos($test, '<h1>Test</h1>') !== false) {
            $this->line('  ✅ Basic headless operation: Working');
        } else {
            $this->line('  ❌ Basic headless operation: Failed');
            $this->line('  Output: '.substr($test, 0, 200));
        }
    }

    /**
     * Test Node directly
     */
    private function testNodeDirectly($nodePath)
    {
        if (! $nodePath) {
            return;
        }

        $this->line('Testing Node.js binary directly:');

        $version = shell_exec("{$nodePath} --version 2>&1");
        $this->line('  Version: '.trim($version ?: 'Unable to get version'));

        $test = shell_exec("{$nodePath} -e \"console.log('Node test successful')\" 2>&1");
        if (strpos($test, 'Node test successful') !== false) {
            $this->line('  ✅ Basic Node operation: Working');
        } else {
            $this->line('  ❌ Basic Node operation: Failed');
            $this->line('  Output: '.trim($test));
        }
    }
}
