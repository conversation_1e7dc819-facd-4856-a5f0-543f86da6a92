<?php

namespace App\Console\Commands;

use App\Models\StudentEnrollment;
use App\Services\BrowsershotService;
use Illuminate\Console\Command;

class DebugAssessmentJobCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'debug:assessment-job {enrollment_id? : The enrollment ID to test with}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Debug assessment job execution with detailed logging';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Debug Assessment Job Execution');
        $this->line('=================================');
        $this->newLine();

        // Test environment variables
        $this->testEnvironmentVariables();

        // Test BrowsershotService
        $this->testBrowsershotService();

        // Test actual job execution
        $enrollmentId = $this->argument('enrollment_id');
        if ($enrollmentId) {
            $this->testJobExecution($enrollmentId);
        } else {
            $this->testJobExecutionWithDummyData();
        }
    }

    /**
     * Test environment variables
     */
    private function testEnvironmentVariables()
    {
        $this->info('Testing Environment Variables:');
        $this->line('------------------------------');

        $envVars = [
            'CHROME_PATH' => env('CHROME_PATH'),
            'NODE_BINARY_PATH' => env('NODE_BINARY_PATH'),
            'NPM_BINARY_PATH' => env('NPM_BINARY_PATH'),
            'BROWSERSHOT_NO_SANDBOX' => env('BROWSERSHOT_NO_SANDBOX'),
            'BROWSERSHOT_TIMEOUT' => env('BROWSERSHOT_TIMEOUT'),
            'BROWSERSHOT_TEMP_DIRECTORY' => env('BROWSERSHOT_TEMP_DIRECTORY'),
        ];

        foreach ($envVars as $key => $value) {
            $status = $value ? "✅ {$value}" : '❌ Not set';
            $this->line("{$key}: {$status}");
        }

        $this->newLine();
    }

    /**
     * Test BrowsershotService
     */
    private function testBrowsershotService()
    {
        $this->info('Testing BrowsershotService Path Detection:');
        $this->line('------------------------------------------');

        $config = config('browsershot', []);

        // Use reflection to access private methods
        $reflection = new \ReflectionClass(BrowsershotService::class);

        $detectChrome = $reflection->getMethod('detectChromePath');
        $detectChrome->setAccessible(true);
        $chromePath = $detectChrome->invoke(null, $config);

        $detectNode = $reflection->getMethod('detectNodePath');
        $detectNode->setAccessible(true);
        $nodePath = $detectNode->invoke(null, $config);

        $detectNpm = $reflection->getMethod('detectNpmPath');
        $detectNpm->setAccessible(true);
        $npmPath = $detectNpm->invoke(null, $config);

        $this->line('Chrome Path Detected: '.($chromePath ?: 'NOT FOUND'));
        $this->line('Node Path Detected: '.($nodePath ?: 'NOT FOUND'));
        $this->line('NPM Path Detected: '.($npmPath ?: 'NOT FOUND'));

        // Verify files exist and are executable
        if ($chromePath) {
            $chromeExists = file_exists($chromePath) && is_executable($chromePath);
            $this->line('Chrome Executable: '.($chromeExists ? '✅ Yes' : '❌ No'));
        }

        if ($nodePath) {
            $nodeExists = file_exists($nodePath) && is_executable($nodePath);
            $this->line('Node Executable: '.($nodeExists ? '✅ Yes' : '❌ No'));
        }

        if ($npmPath) {
            $npmExists = file_exists($npmPath) && is_executable($npmPath);
            $this->line('NPM Executable: '.($npmExists ? '✅ Yes' : '❌ No'));
        }

        $this->newLine();
    }

    /**
     * Test job execution with real enrollment
     */
    private function testJobExecution($enrollmentId)
    {
        $this->info("Testing Job Execution with Enrollment ID: {$enrollmentId}");
        $this->line('--------------------------------------------------');

        try {
            $enrollment = StudentEnrollment::findOrFail($enrollmentId);

            $this->line("Found enrollment: {$enrollment->id}");
            $this->line("Student: {$enrollment->student->first_name} {$enrollment->student->last_name}");
            $this->line("Email: {$enrollment->student->email}");

            $this->newLine();
            $this->info('Executing PDF generation part only...');

            // Test just the PDF generation part
            $this->testPdfGeneration($enrollment);

        } catch (\Exception $e) {
            $this->error("Failed to test job execution: {$e->getMessage()}");
            $this->line("Trace: {$e->getTraceAsString()}");
        }
    }

    /**
     * Test job execution with dummy data
     */
    private function testJobExecutionWithDummyData()
    {
        $this->info('Testing PDF Generation with Dummy Data:');
        $this->line('------------------------------------------');

        // Create dummy data similar to what the job would use
        $data = [
            'student' => (object) [
                'id' => 999,
                'first_name' => 'Test',
                'last_name' => 'Student',
                'email' => '<EMAIL>',
            ],
            'subjects' => collect([
                (object) [
                    'subject_code' => 'TEST-101',
                    'subject_name' => 'Test Subject 1',
                    'units' => 3,
                    'schedule' => 'MWF 9:00-10:00 AM',
                ],
                (object) [
                    'subject_code' => 'TEST-102',
                    'subject_name' => 'Test Subject 2',
                    'units' => 3,
                    'schedule' => 'TTH 2:00-3:30 PM',
                ],
            ]),
            'school_year' => '2024-2025',
            'semester' => '1st Semester',
            'tuition' => (object) [
                'total_amount' => 50000,
                'paid_amount' => 25000,
                'balance' => 25000,
            ],
        ];

        $this->testPdfGenerationWithData($data);
    }

    /**
     * Test PDF generation with enrollment
     */
    private function testPdfGeneration($enrollment)
    {
        try {
            $generalSettings = \App\Models\GeneralSetting::first();

            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $generalSettings->getSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $generalSettings->getSemester() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
            ];

            $this->testPdfGenerationWithData($data);

        } catch (\Exception $e) {
            $this->error("Failed to prepare enrollment data: {$e->getMessage()}");
        }
    }

    /**
     * Test PDF generation with prepared data
     */
    private function testPdfGenerationWithData($data)
    {
        try {
            $this->line('Preparing PDF generation...');

            // Generate test filename
            $randomChars = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10);
            $testFilename = "debug-assmt-{$randomChars}.pdf";
            $testPath = storage_path("app/private/{$testFilename}");

            // Ensure directory exists
            $privateDir = storage_path('app/private');
            if (! \Illuminate\Support\Facades\File::exists($privateDir)) {
                \Illuminate\Support\Facades\File::makeDirectory($privateDir, 0755, true);
                $this->line('Created private directory');
            }

            $this->line("Test file path: {$testPath}");

            // Try to render the HTML first
            $this->line('Rendering HTML view...');

            try {
                $html = view('pdf.assesment-form', $data)->render();
                $this->info('✅ HTML view rendered successfully');
                $this->line('HTML length: '.strlen($html).' characters');
            } catch (\Exception $e) {
                $this->error("❌ Failed to render HTML view: {$e->getMessage()}");

                // Try with simple HTML instead
                $html = $this->getSimpleTestHtml($data);
                $this->line('Using simple test HTML instead');
            }

            // Test PDF generation
            $this->line('Generating PDF with BrowsershotService...');

            $pdfOptions = [
                'format' => 'A4',
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'print_background' => true,
                'landscape' => true,
                'wait_until_network_idle' => true,
                'timeout' => 120,
            ];

            $this->line('PDF Options: '.json_encode($pdfOptions, JSON_PRETTY_PRINT));

            $success = BrowsershotService::generatePdf($html, $testPath, $pdfOptions);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->info('✅ PDF generated successfully!');
                $this->line("   Path: {$testPath}");
                $this->line('   Size: '.number_format($fileSize).' bytes');

                // Keep the file for inspection
                $this->line('   File kept for inspection (not cleaned up)');
            } else {
                $this->error('❌ PDF generation failed');
                $this->line('   Success flag: '.($success ? 'true' : 'false'));
                $this->line('   File exists: '.(file_exists($testPath) ? 'true' : 'false'));
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception during PDF generation:');
            $this->line("   Error: {$e->getMessage()}");
            $this->line("   File: {$e->getFile()}");
            $this->line("   Line: {$e->getLine()}");

            // Check logs for more details
            $this->newLine();
            $this->info('Check the Laravel logs for more detailed error information:');
            $this->line('tail -f storage/logs/laravel.log');
        }
    }

    /**
     * Get simple test HTML
     */
    private function getSimpleTestHtml($data)
    {
        $studentName = is_object($data['student'])
            ? ($data['student']->first_name ?? 'Test').' '.($data['student']->last_name ?? 'Student')
            : 'Test Student';

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>Debug Assessment</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .info { margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Debug Assessment Form</h1>
                <p>School Year: {$data['school_year']} | Semester: {$data['semester']}</p>
            </div>
            <div class='info'>
                <h2>Student Information</h2>
                <p><strong>Name:</strong> {$studentName}</p>
                <p><strong>Generated:</strong> ".now()->format('Y-m-d H:i:s').'</p>
            </div>
        </body>
        </html>';
    }
}
