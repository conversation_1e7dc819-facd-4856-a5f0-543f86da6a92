<?php

namespace App\Console\Commands;

use App\Models\Account;
use App\Models\Student;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class LinkStudentAccounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'students:link-accounts
                            {--dry-run : Show what would be changed without making changes}
                            {--first-year-only : Only process students in their first academic year}
                            {--fix-missing-role : Update accounts with person_id but missing role}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Link student accounts based on matching email addresses';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $firstYearOnly = $this->option('first-year-only');
        $fixMissingRole = $this->option('fix-missing-role');

        $this->info('Starting student account linking process...');
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made to the database');
        }

        // Build the student query
        $studentQuery = Student::whereNotNull('email');

        if ($firstYearOnly) {
            $studentQuery->where('academic_year', 1);
            $this->info('Filtering for first-year students only');
        }

        $students = $studentQuery->get();
        $this->info('Found '.$students->count().' students with email addresses');

        // Fix accounts with missing role but with person_id set
        if ($fixMissingRole) {
            $this->info('Checking for accounts with person_id but missing role...');
            $accountsToFix = Account::whereNotNull('person_id')
                ->where('person_type', 'App\\Models\\Student')
                ->where(function ($query): void {
                    $query->whereNull('role')
                        ->orWhere('role', '');
                })
                ->get();

            $this->info('Found '.$accountsToFix->count().' accounts to fix role');

            $table = [];
            $fixedCount = 0;

            foreach ($accountsToFix as $account) {
                $table[] = [
                    'id' => $account->id,
                    'email' => $account->email,
                    'person_id' => $account->person_id,
                    'old_role' => $account->role ?: 'NULL',
                    'new_role' => 'student',
                ];

                if (! $dryRun) {
                    $account->update(['role' => 'student']);
                    $fixedCount++;

                    Log::info('Fixed account role', [
                        'account_id' => $account->id,
                        'email' => $account->email,
                        'person_id' => $account->person_id,
                    ]);
                }
            }

            if (! empty($table)) {
                $this->table(
                    ['ID', 'Email', 'Person ID', 'Old Role', 'New Role'],
                    $table
                );

                if (! $dryRun) {
                    $this->info("Fixed roles for {$fixedCount} accounts");
                }
            }
        }

        // Process students to link accounts
        $linkTable = [];
        $linkedCount = 0;
        $notFoundCount = 0;

        foreach ($students as $student) {
            // Check if this student already has an associated account
            $hasAccount = Account::where('person_id', $student->id)
                ->where('person_type', 'App\\Models\\Student')
                ->where('role', 'student')
                ->exists();

            if ($hasAccount) {
                // Skip students that already have properly linked accounts
                continue;
            }

            // Try to find an account with the student's email
            $account = Account::where('email', $student->email)->first();

            if ($account) {
                $action = 'Link';
                if ($account->person_id && $account->person_type) {
                    $action = 'Update';
                }

                $linkTable[] = [
                    'student_id' => $student->id,
                    'email' => $student->email,
                    'account_id' => $account->id,
                    'action' => $action,
                    'current_role' => $account->role ?: 'NULL',
                ];

                if (! $dryRun) {
                    $account->update([
                        'role' => 'student',
                        'person_id' => $student->id,
                        'person_type' => 'App\\Models\\Student',
                    ]);
                    $linkedCount++;

                    Log::info('Linked student account', [
                        'student_id' => $student->id,
                        'email' => $student->email,
                        'account_id' => $account->id,
                        'action' => $action,
                    ]);
                }
            } else {
                $notFoundCount++;
            }
        }

        if (! empty($linkTable)) {
            $this->table(
                ['Student ID', 'Email', 'Account ID', 'Action', 'Current Role'],
                $linkTable
            );

            if (! $dryRun) {
                $this->info("Linked {$linkedCount} student accounts");
            }
        }

        $this->info("No accounts found for {$notFoundCount} students");

        if ($dryRun) {
            $this->warn('DRY RUN COMPLETED - Run without --dry-run to apply changes');
        } else {
            $this->info('Account linking process completed');
        }

        return 0;
    }
}
