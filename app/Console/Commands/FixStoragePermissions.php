<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixStoragePermissions extends Command
{
    protected $signature = 'fix:storage-permissions {--check : Only check permissions without fixing}';

    protected $description = 'Fix storage directory permissions for Laravel deployment';

    public function handle()
    {
        $checkOnly = $this->option('check');

        if ($checkOnly) {
            $this->info('Checking storage permissions...');
        } else {
            $this->info('Fixing storage permissions...');
        }

        $storagePath = storage_path();
        $directories = [
            $storagePath,
            $storagePath.'/logs',
            $storagePath.'/framework',
            $storagePath.'/framework/cache',
            $storagePath.'/framework/sessions',
            $storagePath.'/framework/views',
            $storagePath.'/app',
            $storagePath.'/app/private',
            $storagePath.'/app/browsershot-temp',
        ];

        $issues = [];

        foreach ($directories as $dir) {
            if (! File::exists($dir)) {
                $issues[] = "Directory missing: $dir";
                if (! $checkOnly) {
                    File::makeDirectory($dir, 0775, true);
                    $this->line("✅ Created directory: $dir");
                }
            } else {
                $perms = substr(sprintf('%o', fileperms($dir)), -3);
                if ($perms !== '775') {
                    $issues[] = "Directory $dir has permissions $perms (should be 775)";
                    if (! $checkOnly) {
                        chmod($dir, 0775);
                        $this->line("✅ Fixed permissions for: $dir");
                    }
                } else {
                    $this->line("✅ Directory OK: $dir ($perms)");
                }
            }
        }

        // Check ownership (if possible)
        if (function_exists('posix_getpwuid') && function_exists('fileowner')) {
            $owner = posix_getpwuid(fileowner($storagePath));
            $ownerName = $owner['name'] ?? 'unknown';

            if ($ownerName !== 'www-data' && $ownerName !== 'root') {
                $issues[] = "Storage owned by $ownerName (should be www-data or root)";
                if (! $checkOnly) {
                    // Try to change ownership if running as root
                    if (posix_getuid() === 0) {
                        exec("chown -R www-data:www-data $storagePath");
                        $this->line('✅ Changed ownership to www-data');
                    } else {
                        $this->warn(
                            'Cannot change ownership (not running as root)'
                        );
                    }
                }
            } else {
                $this->line("✅ Ownership OK: $ownerName");
            }
        }

        // Check specific log file
        $logFile = storage_path('logs/laravel.log');
        $logIssues = $this->checkLogFile($logFile, $checkOnly);
        $issues = array_merge($issues, $logIssues);

        // Check Chrome temp directories
        $chromeDirs = ['/tmp/chrome-crashpad', '/tmp/chrome-user-data'];
        foreach ($chromeDirs as $dir) {
            if (! is_dir($dir)) {
                $issues[] = "Chrome temp directory missing: $dir";
                if (! $checkOnly) {
                    mkdir($dir, 0777, true);
                    $this->line("✅ Created Chrome temp directory: $dir");
                }
            } else {
                $this->line("✅ Chrome temp directory OK: $dir");
            }
        }

        // Summary
        $this->newLine();
        if (empty($issues)) {
            $this->info('✅ All storage permissions are correct!');
        } else {
            if ($checkOnly) {
                $this->error('❌ Issues found:');
                foreach ($issues as $issue) {
                    $this->line("  • $issue");
                }
                $this->newLine();
                $this->info('Run without --check to fix these issues.');
            } else {
                $this->warn(
                    'Some issues were detected and fixed. You may need to restart your web server.'
                );
            }
        }

        return empty($issues) ? 0 : 1;
    }

    private function checkLogFile($logFile, $checkOnly)
    {
        $issues = [];

        if (! File::exists($logFile)) {
            $issues[] = "Log file missing: $logFile";
            if (! $checkOnly) {
                touch($logFile);
                chmod($logFile, 0664);
                $this->line("✅ Created log file: $logFile");
            }
        } else {
            $perms = substr(sprintf('%o', fileperms($logFile)), -3);
            if (! in_array($perms, ['664', '666'])) {
                $issues[] = "Log file $logFile has permissions $perms (should be 664 or 666)";
                if (! $checkOnly) {
                    chmod($logFile, 0664);
                    $this->line("✅ Fixed log file permissions: $logFile");
                }
            }
        }

        // Test if we can actually write to the log file
        try {
            $testMessage =
                '['.
                date('Y-m-d H:i:s').
                "] Permission test from fix:storage-permissions command\n";
            file_put_contents($logFile, $testMessage, FILE_APPEND | LOCK_EX);
            $this->line('✅ Log file is writable');
        } catch (\Exception $e) {
            $issues[] = 'Cannot write to log file: '.$e->getMessage();
            if (! $checkOnly) {
                // Try various fixes
                if (File::exists($logFile)) {
                    chmod($logFile, 0666);
                    $this->line(
                        'Changed log file permissions to 666 (world writable)'
                    );
                } else {
                    touch($logFile);
                    chmod($logFile, 0666);
                    $this->line('Created log file with 666 permissions');
                }

                // Try writing again
                try {
                    file_put_contents(
                        $logFile,
                        $testMessage,
                        FILE_APPEND | LOCK_EX
                    );
                    $this->line('✅ Log file write test successful after fix');
                } catch (\Exception $e2) {
                    $this->error(
                        '❌ Still cannot write to log file after fix: '.
                            $e2->getMessage()
                    );
                }
            }
        }

        return $issues;
    }
}
