<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;

class TestBrowsershotCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'browsershot:test {--debug : Show debug information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test BrowsershotService functionality and path detection';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing BrowsershotService...');
        $this->newLine();

        if ($this->option('debug')) {
            $this->showSystemInfo();
        }

        $this->testPathDetection();
        $this->testPdfGeneration();

        $this->newLine();
        $this->info('Test completed!');
    }

    /**
     * Show system information for debugging
     */
    private function showSystemInfo()
    {
        $this->info('System Information:');
        $this->line('--------------------');

        $systemInfo = BrowsershotService::getSystemInfo();

        $this->table(
            ['Item', 'Value'],
            [
                ['Chrome (which)', $systemInfo['chrome_which']],
                ['Node (which)', $systemInfo['node_which']],
                ['NPM (which)', $systemInfo['npm_which']],
                ['Chrome (env)', $systemInfo['chrome_env']],
                ['Node (env)', $systemInfo['node_env']],
                ['NPM (env)', $systemInfo['npm_env']],
                ['Chrome (detected)', $systemInfo['chrome_detected']],
                ['Node (detected)', $systemInfo['node_detected']],
                ['NPM (detected)', $systemInfo['npm_detected']],
                ['Temp dir', $systemInfo['temp_dir']],
            ]
        );

        if (! empty($systemInfo['nix_profile_binaries'])) {
            $this->info('Nix Profile Binaries:');
            foreach ($systemInfo['nix_profile_binaries'] as $binary) {
                $this->line("  - {$binary}");
            }
        }

        if (! empty($systemInfo['nix_store_chromium'])) {
            $this->info('Nix Store Chromium:');
            foreach ($systemInfo['nix_store_chromium'] as $package) {
                $this->line("  - {$package}");
            }
        }

        $this->newLine();
    }

    /**
     * Test path detection
     */
    private function testPathDetection()
    {
        $this->info('Testing path detection...');

        $result = BrowsershotService::test();

        if ($result['success']) {
            $this->info('✅ BrowsershotService test passed!');
        } else {
            $this->error('❌ BrowsershotService test failed!');
        }

        $this->table(
            ['Component', 'Status'],
            [
                [
                    'Chrome Path',
                    $result['chrome_path'] !== 'not found'
                        ? "✅ {$result['chrome_path']}"
                        : '❌ Not found',
                ],
                [
                    'Node Path',
                    $result['node_path'] !== 'not found'
                        ? "✅ {$result['node_path']}"
                        : '❌ Not found',
                ],
                [
                    'NPM Path',
                    $result['npm_path'] !== 'not found'
                        ? "✅ {$result['npm_path']}"
                        : '❌ Not found',
                ],
                [
                    'Test PDF Creation',
                    $result['test_pdf_created'] ? '✅ Success' : '❌ Failed',
                ],
            ]
        );

        if (! empty($result['errors'])) {
            $this->error('Errors encountered:');
            foreach ($result['errors'] as $error) {
                $this->line("  - {$error}");
            }
        }

        $this->newLine();
    }

    /**
     * Test PDF generation with assessment-like content
     */
    private function testPdfGeneration()
    {
        $this->info('Testing PDF generation with assessment-like content...');

        try {
            // Create test HTML similar to assessment form
            $testHtml = '
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Test Assessment</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .student-info { margin-bottom: 20px; }
                        .subjects { margin-top: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>Student Assessment Form</h1>
                        <p>School Year: 2024-2025 | Semester: 1st</p>
                    </div>

                    <div class="student-info">
                        <h2>Student Information</h2>
                        <p><strong>Name:</strong> Test Student</p>
                        <p><strong>Student ID:</strong> TEST-001</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                    </div>

                    <div class="subjects">
                        <h2>Enrolled Subjects</h2>
                        <table>
                            <thead>
                                <tr>
                                    <th>Subject Code</th>
                                    <th>Subject Name</th>
                                    <th>Units</th>
                                    <th>Schedule</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TEST-101</td>
                                    <td>Test Subject 1</td>
                                    <td>3</td>
                                    <td>MWF 9:00-10:00 AM</td>
                                </tr>
                                <tr>
                                    <td>TEST-102</td>
                                    <td>Test Subject 2</td>
                                    <td>3</td>
                                    <td>TTH 2:00-3:30 PM</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </body>
                </html>
            ';

            // Generate test PDF
            $testPath = storage_path('app/browsershot-test-assessment.pdf');

            $options = [
                'format' => 'A4',
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'print_background' => true,
                'landscape' => true,
                'wait_until_network_idle' => true,
                'timeout' => 120,
            ];

            $success = BrowsershotService::generatePdf($testHtml, $testPath, $options);

            if ($success && file_exists($testPath)) {
                $fileSize = filesize($testPath);
                $this->info('✅ Assessment-style PDF generated successfully!');
                $this->line("   Path: {$testPath}");
                $this->line('   Size: '.number_format($fileSize).' bytes');

                // Clean up test file
                unlink($testPath);
                $this->line('   Test file cleaned up.');
            } else {
                $this->error('❌ Failed to generate assessment-style PDF');
            }

        } catch (\Exception $e) {
            $this->error('❌ Exception during PDF generation test:');
            $this->line("   Error: {$e->getMessage()}");

            if ($this->option('debug')) {
                $this->line("   Trace: {$e->getTraceAsString()}");
            }
        }
    }
}
