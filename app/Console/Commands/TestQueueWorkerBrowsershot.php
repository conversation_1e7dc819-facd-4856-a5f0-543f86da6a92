<?php

namespace App\Console\Commands;

use App\Jobs\SendAssessmentNotificationJob;
use App\Models\StudentEnrollment;
use App\Services\BrowsershotService;
use Illuminate\Console\Command;

class TestQueueWorkerBrowsershot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:queue-browsershot {--enrollment-id= : Test with specific enrollment ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Browsershot in queue worker environment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing queue worker Browsershot environment...');
        $this->newLine();

        // Test 1: Environment Variables
        $this->info('=== Environment Variables ===');
        $this->line('CHROME_PATH: '.(env('CHROME_PATH') ?: 'not set'));
        $this->line('NODE_BINARY_PATH: '.(env('NODE_BINARY_PATH') ?: 'not set'));
        $this->line('NPM_BINARY_PATH: '.(env('NPM_BINARY_PATH') ?: 'not set'));
        $this->newLine();

        // Test 2: BrowsershotService Detection
        $this->info('=== BrowsershotService Path Detection ===');
        $systemInfo = BrowsershotService::getSystemInfo();
        $this->line('Chrome detected: '.$systemInfo['chrome_detected']);
        $this->line('Node detected: '.$systemInfo['node_detected']);
        $this->line('NPM detected: '.$systemInfo['npm_detected']);
        $this->newLine();

        // Test 3: Quick PDF Generation
        $this->info('=== Quick PDF Test ===');
        try {
            $html = '<h1>Queue Worker Test</h1><p>Generated at: '.now().'</p>';
            $testPath = storage_path('app/queue-worker-test.pdf');

            $success = BrowsershotService::generatePdf($html, $testPath, [
                'format' => 'A4',
                'print_background' => true,
            ]);

            if ($success && file_exists($testPath)) {
                $this->info('✅ PDF generation successful');
                $this->line('File: '.$testPath);
                $this->line('Size: '.filesize($testPath).' bytes');
                unlink($testPath); // Clean up
            } else {
                $this->error('❌ PDF generation failed');
            }
        } catch (\Exception $e) {
            $this->error('❌ PDF generation error: '.$e->getMessage());
        }
        $this->newLine();

        // Test 4: Queue Job Environment (if enrollment ID provided)
        if ($enrollmentId = $this->option('enrollment-id')) {
            $this->info('=== Queue Job Environment Test ===');
            $enrollment = StudentEnrollment::withTrashed()->find($enrollmentId);

            if ($enrollment) {
                $this->info('Testing with enrollment ID: '.$enrollmentId);
                $this->info('Student: '.$enrollment->student?->full_name);

                // Simulate the job environment
                try {
                    // Test the same environment setup as the job
                    if (! env('CHROME_PATH')) {
                        putenv('CHROME_PATH=/root/.nix-profile/bin/chromium');
                    }
                    if (! env('NODE_BINARY_PATH')) {
                        putenv('NODE_BINARY_PATH=/root/.nix-profile/bin/node');
                    }
                    if (! env('NPM_BINARY_PATH')) {
                        putenv('NPM_BINARY_PATH=/root/.nix-profile/bin/npm');
                    }

                    $this->line('Environment variables set for job simulation');
                    $this->line('CHROME_PATH: '.env('CHROME_PATH'));
                    $this->line('NODE_BINARY_PATH: '.env('NODE_BINARY_PATH'));
                    $this->line('NPM_BINARY_PATH: '.env('NPM_BINARY_PATH'));

                    // Create job instance and test environment
                    $job = new SendAssessmentNotificationJob($enrollment);
                    $this->info('✅ Job instantiation successful');

                } catch (\Exception $e) {
                    $this->error('❌ Job environment test failed: '.$e->getMessage());
                }
            } else {
                $this->error('Enrollment ID not found: '.$enrollmentId);
            }
        } else {
            $this->info('Use --enrollment-id option to test with specific enrollment');
        }

        return 0;
    }
}
