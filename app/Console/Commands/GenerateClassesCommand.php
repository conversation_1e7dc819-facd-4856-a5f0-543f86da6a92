<?php

namespace App\Console\Commands;

use App\Models\Classes;
use App\Models\Course;
use App\Models\GeneralSetting;
use App\Models\Room;
use App\Models\Schedule;
use App\Models\Subject;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GenerateClassesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:classes {course_id : The ID of the course} 
                                           {--section=A : The section of the class} 
                                           {--max-slots=50 : Maximum number of slots} 
                                           {--year-levels= : Comma-separated list of year levels to generate (e.g., 1,2,3,4)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate classes for all subjects of a given course';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $courseId = $this->argument('course_id');
        $section = $this->option('section');
        $maxSlots = $this->option('max-slots');
        $yearLevels = $this->option('year-levels');

        $course = Course::find($courseId);

        if (! $course) {
            $this->error("Course with ID $courseId not found.");

            return 1;
        }

        // Get the current academic period from settings
        $settings = GeneralSetting::first();
        if (! $settings) {
            $this->error('General settings not found. Please configure your system settings first.');

            return 1;
        }

        $schoolYear = $settings->getSchoolYearString();
        $semester = $settings->semester;

        // Format school year without spaces for storage (as per the requirement)
        $schoolYearFormatted = str_replace(' ', '', $schoolYear);

        $this->info("Generating classes for {$course->title} (Code: {$course->code})");
        $this->info("Academic Year: $schoolYear, Semester: $semester");

        // Filter by year levels if provided
        $yearLevelsArray = $yearLevels ? explode(',', $yearLevels) : [1, 2, 3, 4];

        // Get all available rooms
        $availableRooms = Room::all();
        if ($availableRooms->isEmpty()) {
            $this->error('No rooms found in the database. Please add rooms first.');

            return 1;
        }

        // Get all subjects for this course matching the year levels and semester
        $subjects = Subject::where('course_id', $courseId)
            ->where('semester', $semester)
            ->whereIn('academic_year', $yearLevelsArray)
            ->get();

        if ($subjects->isEmpty()) {
            $this->error('No subjects found for this course with the specified criteria.');

            return 1;
        }

        $this->info('Found '.$subjects->count().' subjects to process.');

        $bar = $this->output->createProgressBar($subjects->count());
        $bar->start();

        // Start a transaction
        DB::beginTransaction();

        try {
            foreach ($subjects as $subject) {
                // Create the class
                $class = new Classes;
                $class->subject_code = $subject->code;
                $class->academic_year = $subject->academic_year;
                $class->semester = $semester;
                $class->school_year = $schoolYearFormatted;
                $class->course_codes = json_encode([$courseId]);
                $class->section = $section;
                $class->classification = 'college';
                $class->maximum_slots = $maxSlots;
                $class->save();

                // Generate a schedule for this class
                $this->generateScheduleForClass($class, $availableRooms);

                $bar->advance();
            }

            DB::commit();
            $bar->finish();

            $this->newLine();
            $this->info("Successfully generated classes for {$course->title}.");

            return 0;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->newLine();
            $this->error('Error generating classes: '.$e->getMessage());

            return 1;
        }
    }

    /**
     * Generate a non-conflicting schedule for a class
     */
    protected function generateScheduleForClass($class, $availableRooms)
    {
        // Define time slots (8am to 6pm)
        $startHour = 8; // 8am
        $endHour = 18; // 6pm

        // Define possible durations (in minutes)
        $durations = [60, 90];

        // Define possible days
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

        // Randomize to get better distribution
        shuffle($days);

        // Get existing schedules to check for conflicts
        $existingSchedules = Schedule::with('class')->get();

        // Try to find a non-conflicting schedule
        $scheduleCreated = false;
        $maxAttempts = 50; // Prevent infinite loops
        $attempts = 0;

        while (! $scheduleCreated && $attempts < $maxAttempts) {
            $attempts++;

            // Randomly select a day
            $day = $days[array_rand($days)];

            // Randomly select a duration
            $duration = $durations[array_rand($durations)];

            // Randomly select a start hour (ensuring we don't go past end hour)
            $maxStartHour = $endHour - ceil($duration / 60);
            $startTime = rand($startHour, $maxStartHour);

            // Calculate minutes part (0 or 30)
            $startMinutes = rand(0, 1) * 30;

            // Calculate end time
            $endMinutes = ($startMinutes + $duration) % 60;
            $endHour = $startTime + floor(($startMinutes + $duration) / 60);

            // Format times
            $startTimeFormatted = sprintf('%02d:%02d:00', $startTime, $startMinutes);
            $endTimeFormatted = sprintf('%02d:%02d:00', $endHour, $endMinutes);

            // Find an available room
            $availableRoom = $this->findAvailableRoom(
                $day,
                $startTimeFormatted,
                $endTimeFormatted,
                $existingSchedules,
                $availableRooms
            );

            if ($availableRoom) {
                // Create the schedule
                $schedule = new Schedule;
                $schedule->day_of_week = strtolower($day);
                $schedule->start_time = $startTimeFormatted;
                $schedule->end_time = $endTimeFormatted;
                $schedule->class_id = $class->id;
                $schedule->room_id = $availableRoom->id;
                $schedule->save();

                // Add to existing schedules to prevent conflicts
                $existingSchedules->push($schedule);

                $scheduleCreated = true;
            }
        }

        if (! $scheduleCreated) {
            throw new \Exception("Could not find a non-conflicting schedule for subject {$class->subject_code} after $maxAttempts attempts.");
        }

        return true;
    }

    /**
     * Find an available room for a given time slot
     */
    protected function findAvailableRoom($day, $startTime, $endTime, $existingSchedules, $availableRooms)
    {
        // Convert times to Carbon objects for easier comparison
        $startTimeObj = Carbon::parse($startTime);
        $endTimeObj = Carbon::parse($endTime);

        // Create a copy to avoid modifying the original
        $rooms = $availableRooms->shuffle(); // Randomize room selection

        foreach ($rooms as $room) {
            $hasConflict = false;

            // Check each existing schedule for conflicts
            foreach ($existingSchedules as $schedule) {
                // Skip if different day
                if (strtolower($schedule->day_of_week) !== strtolower($day)) {
                    continue;
                }

                // Skip if different room
                if ($schedule->room_id !== $room->id) {
                    continue;
                }

                // Convert schedule times to Carbon objects
                $scheduleStart = Carbon::parse($schedule->start_time);
                $scheduleEnd = Carbon::parse($schedule->end_time);

                // Check for overlap
                if (
                    ($startTimeObj >= $scheduleStart && $startTimeObj < $scheduleEnd) ||
                    ($endTimeObj > $scheduleStart && $endTimeObj <= $scheduleEnd) ||
                    ($startTimeObj <= $scheduleStart && $endTimeObj >= $scheduleEnd)
                ) {
                    $hasConflict = true;
                    break;
                }
            }

            if (! $hasConflict) {
                return $room;
            }
        }

        return null; // No available room found
    }
}
