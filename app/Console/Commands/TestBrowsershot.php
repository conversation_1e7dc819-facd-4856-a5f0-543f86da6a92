<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class TestBrowsershot extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:browsershot {--detailed : Show detailed system information}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Browsershot PDF generation';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Browsershot PDF generation...');
        $this->newLine();

        // Show system information if requested
        if ($this->option('detailed')) {
            $this->showSystemInfo();
            $this->newLine();
        }

        // Run the test
        $result = BrowsershotService::test();

        // Display results
        $this->displayTestResults($result);

        // Additional manual test
        $this->info('Running additional manual test...');
        $this->runManualTest();

        return 0;
    }

    /**
     * Show detailed system information.
     */
    private function showSystemInfo()
    {
        $this->info('System Information:');
        $this->line('==================');

        $info = BrowsershotService::getSystemInfo();

        $this->table(
            ['Component', 'Which Command', 'Environment Variable'],
            [
                ['Chrome/Chromium', $info['chrome_which'], $info['chrome_env']],
                ['Node.js', $info['node_which'], $info['node_env']],
                ['NPM', $info['npm_which'], $info['npm_env']],
            ]
        );

        $this->info('Temporary Directory: '.$info['temp_dir']);

        if (! empty($info['nix_store_chromium'])) {
            $this->info('Chromium in Nix Store:');
            foreach ($info['nix_store_chromium'] as $item) {
                $this->line('  - '.$item);
            }
        }
    }

    /**
     * Display test results.
     */
    private function displayTestResults(array $result)
    {
        if ($result['success']) {
            $this->info('✅ Browsershot test PASSED!');
        } else {
            $this->error('❌ Browsershot test FAILED!');
        }

        $this->newLine();
        $this->info('Test Details:');
        $this->line('=============');
        $this->line('Chrome Path: '.$result['chrome_path']);
        $this->line('Node Path: '.$result['node_path']);
        $this->line('NPM Path: '.$result['npm_path']);
        $this->line(
            'Test PDF Created: '.($result['test_pdf_created'] ? 'Yes' : 'No')
        );

        if (! empty($result['errors'])) {
            $this->newLine();
            $this->error('Errors encountered:');
            foreach ($result['errors'] as $error) {
                $this->line('  - '.$error);
            }
        }
    }

    /**
     * Run a manual test with detailed output.
     */
    private function runManualTest()
    {
        $html =
            '<h1>Manual Test</h1><p>This is a manual test PDF generated by Browsershot.</p><p>Current time: '.
            now().
            '</p>';
        $outputPath = storage_path('app/browsershot_manual_test.pdf');

        try {
            $this->info('Generating PDF using auto-detected paths...');

            $success = BrowsershotService::generatePdf($html, $outputPath, [
                'format' => 'A4',
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
            ]);

            if ($success && file_exists($outputPath)) {
                $fileSize = File::size($outputPath);
                $this->info('✅ Manual test successful!');
                $this->info("PDF saved to: {$outputPath}");
                $this->info(
                    'File size: '.number_format($fileSize).' bytes'
                );

                // Optionally keep the file for inspection
                $this->info('You can inspect the generated PDF file.');
            } else {
                $this->error(
                    '❌ Manual test failed: PDF file was not created or is empty.'
                );
            }
        } catch (\Exception $e) {
            $this->error('❌ Manual test failed with exception:');
            $this->error($e->getMessage());

            // Log the full exception for debugging
            \Illuminate\Support\Facades\Log::error(
                'Manual Browsershot Test Failed',
                [
                    'exception' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]
            );
        }
    }
}
