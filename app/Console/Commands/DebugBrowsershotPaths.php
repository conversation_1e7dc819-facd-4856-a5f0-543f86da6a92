<?php

namespace App\Console\Commands;

use App\Services\BrowsershotService;
use Illuminate\Console\Command;

class DebugBrowsershotPaths extends Command
{
    protected $signature = 'debug:browsershot-paths';

    protected $description = 'Debug Browsershot path detection in detail';

    public function handle()
    {
        $this->info('=== Debugging Browsershot Path Detection ===');
        $this->newLine();

        // Test 1: Current environment variables
        $this->info('1. Current Environment Variables:');
        $this->line('CHROME_PATH: '.(env('CHROME_PATH') ?: 'not set'));
        $this->line('NODE_BINARY_PATH: '.(env('NODE_BINARY_PATH') ?: 'not set'));
        $this->line('NPM_BINARY_PATH: '.(env('NPM_BINARY_PATH') ?: 'not set'));
        $this->newLine();

        // Test 2: Check file existence
        $this->info('2. File Existence Check:');
        $paths = [
            '/root/.nix-profile/bin/chromium',
            '/nix/var/nix/profiles/default/bin/chromium',
            '/root/.nix-profile/bin/node',
            '/nix/var/nix/profiles/default/bin/node',
            '/root/.nix-profile/bin/npm',
            '/nix/var/nix/profiles/default/bin/npm',
        ];

        foreach ($paths as $path) {
            $exists = file_exists($path);
            $executable = $exists && is_executable($path);
            $version = '';
            if ($executable) {
                $version = ' ['.trim(shell_exec("$path --version 2>/dev/null") ?: 'no version').']';
            }
            $this->line(sprintf(
                '%-40s %s %s%s',
                $path,
                $exists ? '✓ exists' : '✗ missing',
                $executable ? '✓ executable' : '✗ not executable',
                $version
            ));
        }
        $this->newLine();

        // Test 3: Which command results
        $this->info('3. Which Command Results:');
        $binaries = ['chromium', 'node', 'npm'];
        foreach ($binaries as $binary) {
            $path = trim(shell_exec("which $binary 2>/dev/null") ?: 'not found');
            $this->line("which $binary: $path");
        }
        $this->newLine();

        // Test 4: Force set environment and test
        $this->info('4. Testing with forced environment variables:');
        putenv('CHROME_PATH=/root/.nix-profile/bin/chromium');
        putenv('NODE_BINARY_PATH=/root/.nix-profile/bin/node');
        putenv('NPM_BINARY_PATH=/root/.nix-profile/bin/npm');

        $_ENV['CHROME_PATH'] = '/root/.nix-profile/bin/chromium';
        $_ENV['NODE_BINARY_PATH'] = '/root/.nix-profile/bin/node';
        $_ENV['NPM_BINARY_PATH'] = '/root/.nix-profile/bin/npm';

        $this->line('Environment variables set to:');
        $this->line('CHROME_PATH: '.env('CHROME_PATH'));
        $this->line('NODE_BINARY_PATH: '.env('NODE_BINARY_PATH'));
        $this->line('NPM_BINARY_PATH: '.env('NPM_BINARY_PATH'));
        $this->newLine();

        // Test 5: Test BrowsershotService detection
        $this->info('5. BrowsershotService Detection:');
        $systemInfo = BrowsershotService::getSystemInfo();
        $this->line('Chrome detected: '.$systemInfo['chrome_detected']);
        $this->line('Node detected: '.$systemInfo['node_detected']);
        $this->line('NPM detected: '.$systemInfo['npm_detected']);
        $this->newLine();

        // Test 6: Test actual PDF generation
        $this->info('6. PDF Generation Test:');
        try {
            $html = '<h1>Debug Test</h1><p>Testing forced paths</p>';
            $testPath = storage_path('app/debug-browsershot-test.pdf');

            $success = BrowsershotService::generatePdf($html, $testPath, [
                'format' => 'A4',
                'timeout' => 60,
            ]);

            if ($success && file_exists($testPath)) {
                $this->info('✅ PDF generation successful with forced paths');
                $this->line('File: '.$testPath);
                $this->line('Size: '.filesize($testPath).' bytes');

                // Optional: keep the file for inspection
                $this->info('PDF saved for inspection');
            } else {
                $this->error('❌ PDF generation failed even with forced paths');
            }
        } catch (\Exception $e) {
            $this->error('❌ PDF generation error: '.$e->getMessage());
            $this->line('Stack trace: '.$e->getTraceAsString());
        }

        return 0;
    }
}
