<?php

namespace App\Console\Commands;

use App\Models\Classes;
use App\Models\GeneralSetting;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FixClassSlots extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'classes:fix-slots 
                            {--default=50 : Default maximum slots to set if not specified}
                            {--current-period : Only fix classes for the current school year and semester}
                            {--dry-run : Show what would be changed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix maximum slots for classes by setting appropriate values';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $defaultMaxSlots = $this->option('default');
        $currentPeriodOnly = $this->option('current-period');
        $dryRun = $this->option('dry-run');

        $this->info('Starting class slot fix...');
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No changes will be made to the database');
        }

        // Build query based on options
        $query = Classes::query();

        if ($currentPeriodOnly) {
            $settings = GeneralSetting::first();
            if (! $settings) {
                $this->error('General settings not found. Cannot filter by current period.');

                return 1;
            }

            $schoolYear = $settings->getSchoolYear();
            $semester = $settings->semester;

            $this->info("Filtering for current period: School Year {$schoolYear}, Semester {$semester}");
            $query->where('school_year', $schoolYear)
                ->where('semester', $semester);
        }

        // Find classes with null, zero, or negative maximum_slots
        $classesToFix = $query->where(function ($q): void {
            $q->whereNull('maximum_slots')
                ->orWhere('maximum_slots', 0)
                ->orWhere('maximum_slots', '<', 0);
        })->get();

        $count = $classesToFix->count();
        $this->info("Found {$count} classes that need fixing");

        if ($count === 0) {
            $this->info('No classes need fixing.');

            return 0;
        }

        $table = [];
        $fixedCount = 0;

        foreach ($classesToFix as $class) {
            $oldValue = $class->maximum_slots ?? 'NULL';
            $newValue = $defaultMaxSlots;

            $table[] = [
                'id' => $class->id,
                'subject' => $class->subject_code,
                'section' => $class->section,
                'old_max_slots' => $oldValue,
                'new_max_slots' => $newValue,
            ];

            if (! $dryRun) {
                $class->maximum_slots = $newValue;
                $class->save();
                $fixedCount++;

                Log::info('Fixed class slots', [
                    'class_id' => $class->id,
                    'subject' => $class->subject_code,
                    'section' => $class->section,
                    'old_value' => $oldValue,
                    'new_value' => $newValue,
                ]);
            }
        }

        // Display table of changes
        $this->table(
            ['ID', 'Subject', 'Section', 'Old Max Slots', 'New Max Slots'],
            $table
        );

        if ($dryRun) {
            $this->warn('DRY RUN COMPLETED - Run without --dry-run to apply changes');
        } else {
            $this->info("Fixed {$fixedCount} classes with proper maximum slots values");
        }

        return 0;
    }
}
