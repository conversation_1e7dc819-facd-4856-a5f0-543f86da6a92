<?php

namespace App\Services;

use App\Models\Room;
use App\Models\Schedule;
use Carbon\Carbon;

class TimetableConflictResolutionService
{
    protected TimetableConflictService $conflictService;

    protected GeneralSettingsService $settingsService;

    public function __construct(
        TimetableConflictService $conflictService,
        GeneralSettingsService $settingsService
    ) {
        $this->conflictService = $conflictService;
        $this->settingsService = $settingsService;
    }

    /**
     * Get suggestions to resolve a specific conflict
     */
    public function getConflictResolutionSuggestions(array $conflict): array
    {
        $suggestions = [];
        $conflictType = $conflict['type'] ?? 'unknown';

        switch ($conflictType) {
            case 'time_room':
                $suggestions = $this->getRoomTimeConflictSuggestions($conflict);
                break;
            case 'faculty':
                $suggestions = $this->getFacultyConflictSuggestions($conflict);
                break;
            case 'student':
                $suggestions = $this->getStudentConflictSuggestions($conflict);
                break;
        }

        return $suggestions;
    }

    /**
     * Get suggestions for room/time conflicts
     */
    protected function getRoomTimeConflictSuggestions(array $conflict): array
    {
        $suggestions = [];

        if (empty($conflict['conflicts'])) {
            return $suggestions;
        }

        $firstConflict = $conflict['conflicts'][0];
        $schedule1 = $firstConflict['schedule1'];
        $schedule2 = $firstConflict['schedule2'];

        // Suggest alternative rooms
        $alternativeRooms = $this->findAlternativeRooms($schedule1);
        if (! empty($alternativeRooms)) {
            $suggestions[] = [
                'type' => 'alternative_room',
                'title' => 'Use Alternative Room',
                'description' => 'Move one of the classes to an available room',
                'options' => $alternativeRooms,
                'priority' => 'high',
            ];
        }

        // Suggest alternative time slots
        $alternativeTimeSlots = $this->findAlternativeTimeSlots($schedule1);
        if (! empty($alternativeTimeSlots)) {
            $suggestions[] = [
                'type' => 'alternative_time',
                'title' => 'Reschedule to Different Time',
                'description' => 'Move one of the classes to an available time slot',
                'options' => $alternativeTimeSlots,
                'priority' => 'medium',
            ];
        }

        // Suggest splitting the class
        $suggestions[] = [
            'type' => 'split_class',
            'title' => 'Split Class Session',
            'description' => 'Divide the class into multiple shorter sessions',
            'options' => $this->getSplitClassOptions($schedule1),
            'priority' => 'low',
        ];

        return $suggestions;
    }

    /**
     * Get suggestions for faculty conflicts
     */
    protected function getFacultyConflictSuggestions(array $conflict): array
    {
        $suggestions = [];

        // Suggest alternative faculty
        $suggestions[] = [
            'type' => 'alternative_faculty',
            'title' => 'Assign Alternative Faculty',
            'description' => 'Assign a different qualified faculty member',
            'options' => $this->findAlternativeFaculty($conflict),
            'priority' => 'high',
        ];

        // Suggest rescheduling
        $suggestions[] = [
            'type' => 'reschedule_faculty',
            'title' => 'Reschedule Classes',
            'description' => 'Move one of the classes to when faculty is available',
            'options' => $this->findFacultyAvailableSlots($conflict),
            'priority' => 'medium',
        ];

        return $suggestions;
    }

    /**
     * Get suggestions for student conflicts
     */
    protected function getStudentConflictSuggestions(array $conflict): array
    {
        $suggestions = [];

        // Suggest alternative sections
        $suggestions[] = [
            'type' => 'alternative_section',
            'title' => 'Move to Different Section',
            'description' => 'Transfer students to non-conflicting sections',
            'options' => $this->findAlternativeSections($conflict),
            'priority' => 'medium',
        ];

        // Suggest class rescheduling
        $suggestions[] = [
            'type' => 'reschedule_class',
            'title' => 'Reschedule One Class',
            'description' => 'Move one of the conflicting classes to a different time',
            'options' => $this->findNonConflictingTimeSlots($conflict),
            'priority' => 'high',
        ];

        return $suggestions;
    }

    /**
     * Find alternative rooms for a schedule
     */
    protected function findAlternativeRooms($schedule): array
    {
        $currentRoomId = $schedule['room_id'];
        $dayOfWeek = $schedule['day_of_week'];
        $startTime = $schedule['start_time'];
        $endTime = $schedule['end_time'];

        // Find rooms that are available at the same time
        $availableRooms = Room::whereNotIn('id', [$currentRoomId])
            ->whereDoesntHave('schedules', function ($query) use ($dayOfWeek, $startTime, $endTime): void {
                $query->where('day_of_week', $dayOfWeek)
                    ->where(function ($q) use ($startTime, $endTime): void {
                        $q->whereBetween('start_time', [$startTime, $endTime])
                            ->orWhereBetween('end_time', [$startTime, $endTime])
                            ->orWhere(function ($subQ) use ($startTime, $endTime): void {
                                $subQ->where('start_time', '<=', $startTime)
                                    ->where('end_time', '>=', $endTime);
                            });
                    });
            })
            ->get();

        return $availableRooms->map(function ($room) {
            return [
                'id' => $room->id,
                'name' => $room->name,
                'capacity' => $room->capacity ?? 'N/A',
                'type' => $room->type ?? 'Standard',
            ];
        })->toArray();
    }

    /**
     * Find alternative time slots for a schedule
     */
    protected function findAlternativeTimeSlots($schedule): array
    {
        $roomId = $schedule['room_id'];
        $dayOfWeek = $schedule['day_of_week'];
        $duration = Carbon::parse($schedule['end_time'])->diffInMinutes(Carbon::parse($schedule['start_time']));

        // Define possible time slots (you can make this configurable)
        $possibleSlots = [
            '07:00:00', '08:00:00', '09:00:00', '10:00:00', '11:00:00',
            '13:00:00', '14:00:00', '15:00:00', '16:00:00', '17:00:00',
            '18:00:00', '19:00:00',
        ];

        $availableSlots = [];

        foreach ($possibleSlots as $startTime) {
            $endTime = Carbon::parse($startTime)->addMinutes($duration)->format('H:i:s');

            // Check if this slot is available
            $isAvailable = ! Schedule::where('room_id', $roomId)
                ->where('day_of_week', $dayOfWeek)
                ->where(function ($query) use ($startTime, $endTime): void {
                    $query->whereBetween('start_time', [$startTime, $endTime])
                        ->orWhereBetween('end_time', [$startTime, $endTime])
                        ->orWhere(function ($q) use ($startTime, $endTime): void {
                            $q->where('start_time', '<=', $startTime)
                                ->where('end_time', '>=', $endTime);
                        });
                })
                ->exists();

            if ($isAvailable) {
                $availableSlots[] = [
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                    'formatted' => Carbon::parse($startTime)->format('g:i A').' - '.Carbon::parse($endTime)->format('g:i A'),
                ];
            }
        }

        return $availableSlots;
    }

    /**
     * Get split class options
     */
    protected function getSplitClassOptions($schedule): array
    {
        $duration = Carbon::parse($schedule['end_time'])->diffInMinutes(Carbon::parse($schedule['start_time']));

        $options = [];

        if ($duration >= 120) { // 2 hours or more
            $options[] = [
                'type' => 'split_half',
                'description' => 'Split into two 1-hour sessions',
                'sessions' => 2,
                'duration_each' => 60,
            ];
        }

        if ($duration >= 180) { // 3 hours or more
            $options[] = [
                'type' => 'split_third',
                'description' => 'Split into three 1-hour sessions',
                'sessions' => 3,
                'duration_each' => 60,
            ];
        }

        return $options;
    }

    /**
     * Find alternative faculty for a conflict
     */
    protected function findAlternativeFaculty($conflict): array
    {
        // This would need to be implemented based on your faculty qualification system
        return [
            [
                'id' => 'placeholder',
                'name' => 'Alternative faculty suggestions would be implemented here',
                'qualifications' => 'Based on subject expertise',
            ],
        ];
    }

    /**
     * Find faculty available slots
     */
    protected function findFacultyAvailableSlots($conflict): array
    {
        // This would analyze faculty schedules to find available time slots
        return [
            [
                'day' => 'Monday',
                'time' => '2:00 PM - 3:00 PM',
                'available' => true,
            ],
        ];
    }

    /**
     * Find alternative sections
     */
    protected function findAlternativeSections($conflict): array
    {
        // This would find other sections of the same subject with available slots
        return [];
    }

    /**
     * Find non-conflicting time slots
     */
    protected function findNonConflictingTimeSlots($conflict): array
    {
        // This would analyze all student schedules to find non-conflicting times
        return [];
    }

    /**
     * Apply a resolution suggestion
     */
    public function applyResolution(array $suggestion, array $conflictData): bool
    {
        try {
            switch ($suggestion['type']) {
                case 'alternative_room':
                    return $this->applyRoomChange($suggestion, $conflictData);
                case 'alternative_time':
                    return $this->applyTimeChange($suggestion, $conflictData);
                case 'split_class':
                    return $this->applySplitClass($suggestion, $conflictData);
                default:
                    return false;
            }
        } catch (\Exception $e) {
            \Log::error('Failed to apply conflict resolution: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Apply room change resolution
     */
    protected function applyRoomChange(array $suggestion, array $conflictData): bool
    {
        // Implementation would update the schedule with new room
        return true;
    }

    /**
     * Apply time change resolution
     */
    protected function applyTimeChange(array $suggestion, array $conflictData): bool
    {
        // Implementation would update the schedule with new time
        return true;
    }

    /**
     * Apply split class resolution
     */
    protected function applySplitClass(array $suggestion, array $conflictData): bool
    {
        // Implementation would create multiple schedule entries
        return true;
    }
}
