<?php

use App\Models\Classes;
use App\Models\Subject;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update classes that still have null subject_id, handling trailing spaces
        $classes = Classes::whereNull('subject_id')
            ->whereNotNull('subject_code')
            ->whereNotNull('course_codes')
            ->get();

        foreach ($classes as $class) {
            $courseIds = is_array($class->course_codes) ? $class->course_codes : json_decode($class->course_codes, true);

            if (! empty($courseIds)) {
                $subjectCode = $class->subject_code;
                $subject = null;

                // First, try exact match
                $subject = Subject::where('code', $subjectCode)
                    ->whereIn('course_id', $courseIds)
                    ->first();

                // If not found, try with trimmed code
                if (! $subject) {
                    $trimmedCode = trim($subjectCode);
                    $subject = Subject::where('code', $trimmedCode)
                        ->whereIn('course_id', $courseIds)
                        ->first();
                }

                // If still not found, try finding subjects with similar codes (handling trailing spaces)
                if (! $subject) {
                    $trimmedCode = trim($subjectCode);
                    $subject = Subject::whereRaw('TRIM(code) = ?', [$trimmedCode])
                        ->whereIn('course_id', $courseIds)
                        ->first();
                }

                // If still not found, try finding any subject with the code in any of the courses
                if (! $subject) {
                    $trimmedCode = trim($subjectCode);
                    $subject = Subject::where(function ($query) use ($subjectCode, $trimmedCode) {
                        $query->where('code', $subjectCode)
                            ->orWhere('code', $trimmedCode)
                            ->orWhereRaw('TRIM(code) = ?', [$trimmedCode]);
                    })
                        ->whereIn('course_id', $courseIds)
                        ->first();
                }

                if ($subject) {
                    $class->subject_id = $subject->id;
                    $class->save();

                    echo "Updated class {$class->id}: subject_code '{$class->subject_code}' -> subject_id {$subject->id} ('{$subject->code}' from course {$subject->course_id})\n";
                } else {
                    echo "Warning: Could not find subject for class {$class->id} with code '{$class->subject_code}' and courses ".json_encode($courseIds)."\n";

                    // Show available subjects for debugging
                    $availableSubjects = Subject::whereIn('course_id', $courseIds)
                        ->where(function ($query) use ($subjectCode) {
                            $trimmedCode = trim($subjectCode);
                            $query->where('code', 'LIKE', '%'.$trimmedCode.'%')
                                ->orWhere('code', 'LIKE', '%'.$subjectCode.'%');
                        })
                        ->get(['id', 'code', 'title', 'course_id']);

                    if ($availableSubjects->count() > 0) {
                        echo "  Available similar subjects in these courses:\n";
                        foreach ($availableSubjects as $availableSubject) {
                            echo "    ID: {$availableSubject->id}, Code: '{$availableSubject->code}', Title: {$availableSubject->title}, Course: {$availableSubject->course_id}\n";
                        }
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is additive, no need to reverse
    }
};
