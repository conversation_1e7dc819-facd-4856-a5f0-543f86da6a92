<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update all existing classes that have course_codes but NULL classification to 'college'
        // These are classes that were created from College courses before the SHS enhancement

        DB::table('classes')
            ->whereNull('classification')
            ->whereNotNull('course_codes')
            ->update(['classification' => 'college']);

        // Also update any remaining NULL classifications to 'college' as default
        // since the system was originally College-only
        DB::table('classes')
            ->whereNull('classification')
            ->update(['classification' => 'college']);

        echo "Updated existing classes classification to 'college'\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert the classification updates by setting them back to NULL
        // Only revert classes that don't have SHS-specific fields
        DB::table('classes')
            ->where('classification', 'college')
            ->whereNull('shs_track_id')
            ->whereNull('shs_strand_id')
            ->whereNull('grade_level')
            ->update(['classification' => null]);
    }
};
