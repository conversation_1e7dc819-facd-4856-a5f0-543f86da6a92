<?php

use App\Models\Classes;
use App\Models\Subject;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing classes to populate subject_id based on subject_code and course_codes
        $classes = Classes::whereNull('subject_id')
            ->whereNotNull('subject_code')
            ->whereNotNull('course_codes')
            ->get();

        foreach ($classes as $class) {
            $courseIds = is_array($class->course_codes) ? $class->course_codes : json_decode($class->course_codes, true);

            if (! empty($courseIds)) {
                // Find the correct subject based on code and course
                // Trim the subject code to handle cases where extra spaces were added
                $trimmedSubjectCode = trim($class->subject_code);

                $subject = Subject::where('code', $trimmedSubjectCode)
                    ->whereIn('course_id', $courseIds)
                    ->first();

                // If not found with trimmed code, try with original code
                if (! $subject) {
                    $subject = Subject::where('code', $class->subject_code)
                        ->whereIn('course_id', $courseIds)
                        ->first();
                }

                if ($subject) {
                    $class->subject_id = $subject->id;
                    $class->save();

                    echo "Updated class {$class->id}: subject_code '{$class->subject_code}' -> subject_id {$subject->id} (course {$subject->course_id})\n";
                } else {
                    echo "Warning: Could not find subject for class {$class->id} with code '{$class->subject_code}' and courses ".json_encode($courseIds)."\n";

                    // Try to find any subject with similar code (handling extra spaces)
                    $similarSubjects = Subject::where('code', 'LIKE', '%'.$trimmedSubjectCode.'%')
                        ->whereIn('course_id', $courseIds)
                        ->get();

                    if ($similarSubjects->count() > 0) {
                        echo '  Found similar subjects: '.$similarSubjects->pluck('code')->join(', ')."\n";
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Set subject_id back to null for classes that were updated
        DB::table('classes')
            ->whereNotNull('subject_id')
            ->update(['subject_id' => null]);
    }
};
