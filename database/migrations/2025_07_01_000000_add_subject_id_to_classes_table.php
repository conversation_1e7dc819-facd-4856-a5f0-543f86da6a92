<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('classes', function (Blueprint $table) {
            // Add subject_id column to store the specific subject ID
            // This will help resolve issues with duplicate subject codes across different courses
            $table->foreignId('subject_id')->nullable()->after('id')->constrained('subject')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('classes', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['subject_id']);

            // Drop the column
            $table->dropColumn('subject_id');
        });
    }
};
