<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class FacultyRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create faculty role if it doesn't exist
        $facultyRole = Role::firstOrCreate(['name' => 'faculty']);

        // Define faculty permissions
        $permissions = [
            'view_grades',
            'edit_grades',
            'finalize_grades',
            'view_class_list',
            'view_student_info',
        ];

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to faculty role
        $facultyRole->givePermissionTo($permissions);
    }
}
