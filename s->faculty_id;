[90m= [39m[34;4mApp\Models\Classes[39;24m {#8371
    [34mid[39m: [35m372[39m,
    [34msubject_code[39m: "[32mCORE-ORALCOM[39m",
    [34mfaculty_id[39m: [36mnull[39m,
    [34macademic_year[39m: [36mnull[39m,
    [34msemester[39m: "[32m1[39m",
    [34mschedule_id[39m: [36mnull[39m,
    [34mschool_year[39m: "[32m2025 - 2026[39m",
    [34mcourse_codes[39m: [36mnull[39m,
    [34msection[39m: "[32mC[39m",
    [34mroom_id[39m: [35m33[39m,
    [34mclassification[39m: "[32mshs[39m",
    [34mmaximum_slots[39m: [35m40[39m,
    [34mcreated_at[39m: "[32m2025-07-02 00:18:23[39m",
    [34mupdated_at[39m: "[32m2025-07-02 00:18:23[39m",
    [34mshs_track_id[39m: [35m2[39m,
    [34mshs_strand_id[39m: [35m1[39m,
    [34mgrade_level[39m: "[32mGrade 11[39m",
    [34msubject_id[39m: [36mnull[39m,
    [34mFaculty[39m: [36mnull[39m,
  }

