# Class Generator for DCCP Admin

This tool allows administrators to automatically generate classes for an entire course curriculum based on subjects in the database.

## Features

- Automatically generates classes for all subjects in a course
- Creates non-conflicting schedules for each class
- Assigns appropriate rooms for each schedule
- Follows scheduling constraints (8am-6pm, Monday-Saturday)
- Assigns class durations of either 60 or 90 minutes

## Usage

### The Interactive Way

Use the interactive test command:

```bash
php artisan generate:classes-test
```

This command will:
1. Show you all available courses with their IDs
2. Prompt you to select a course ID
3. Ask for section name (default: A)
4. Ask for maximum slots per class (default: 50)
5. Allow you to filter by specific year levels
6. Confirm before executing

### The Direct Way

You can also run the command directly with arguments and options:

```bash
php artisan generate:classes {course_id} [--section=A] [--max-slots=50] [--year-levels=1,2,3,4]
```

#### Arguments:

- `course_id`: The ID of the course to generate classes for (required)

#### Options:

- `--section`: The section name for the classes (default: A)
- `--max-slots`: Maximum number of slots per class (default: 50)
- `--year-levels`: Comma-separated list of year levels to generate (1,2,3,4). If not provided, all year levels will be generated.

## Example

Generate classes for course ID 5 with section B and 40 maximum slots:

```bash
php artisan generate:classes 5 --section=B --max-slots=40
```

Generate only 1st and 2nd year classes for course ID 3:

```bash
php artisan generate:classes 3 --year-levels=1,2
```

## Important Notes

1. The command will use the current academic period (school year and semester) as defined in the system settings.
2. The school year format in the database will not have spaces (e.g., "2024-2025" instead of "2024 - 2025").
3. Classes will be created with the proper relationship to rooms and schedules.
4. All generated schedules will be checked for room conflicts.
5. If the command cannot create a non-conflicting schedule after multiple attempts, it will fail and report an error.

## Technical Details

The command:
1. Fetches all subjects for the given course, semester, and year levels
2. Creates a class record for each subject
3. Generates a schedule for each class with a non-conflicting room and time slot
4. Uses a transaction to ensure data integrity (all or nothing) 