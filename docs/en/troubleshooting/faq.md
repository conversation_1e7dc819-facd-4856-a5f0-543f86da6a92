---
title: Frequently Asked Questions
icon: heroicon-o-question-mark-circle
order: 1
group: Troubleshooting
---

# Frequently Asked Questions

This section covers the most common questions and issues encountered when using the DCCP Admin enrollment system.

## General Questions

### Q: How do I access the enrollment system?
**A:** Navigate to the admin panel and look for "Student Enrollments" in the sidebar. Click "New Enrollment" to start the enrollment process.

### Q: What permissions do I need to enroll students?
**A:** You need:
- Access to Student Enrollment Resource
- Create enrollment permissions
- View student records permissions

### Q: Can I enroll multiple students at once?
**A:** Currently, the system requires individual enrollment for each student. However, you can use the bulk operations for specific tasks like class assignments.

## Student-Related Questions

### Q: What if a student doesn't appear in the search?
**A:** Check the following:
1. Verify the student record exists in the system
2. Check spelling of the name or ID number
3. Ensure the student record is active
4. Contact your system administrator if the student should exist

### Q: Can I enroll a student who hasn't paid previous balances?
**A:** The system allows enrollment regardless of previous balances, but check your institution's policy on outstanding payments before proceeding.

### Q: How do I handle transfer students?
**A:**
1. Ensure the transfer student has a complete record in the system
2. Verify their academic standing and credits
3. Enroll them following the standard process
4. Consider any credit transfers in their academic planning

## Subject and Course Questions

### Q: Why can't I find a specific subject?
**A:** Possible reasons:
- Subject is not offered this semester
- Subject is not assigned to the student's course
- Academic period settings are incorrect
- Subject has been deactivated

### Q: What's the difference between lecture and laboratory units?
**A:**
- **Lecture Units**: Theoretical instruction, calculated using total units for fees
- **Laboratory Units**: Practical sessions, calculated separately for fees
- Both contribute to the total academic units for the subject

### Q: How do I handle subjects with the same code in different courses?
**A:** Add extra spaces at the end of duplicate subject codes for disambiguation (e.g., "CS101" vs "CS101 ").

## Tuition and Fee Questions

### Q: Why are my tuition calculations incorrect?
**A:** Check these common issues:
1. Course fee rates are properly configured
2. Subject unit assignments are correct
3. Discount is being applied only to lecture fees
4. NSTP subjects are getting the 50% discount
5. Modular subjects are using the fixed ₱2,400 fee

### Q: How are discounts applied?
**A:**
- Discounts apply **only to lecture fees**
- Laboratory fees are never discounted
- Miscellaneous fees are never discounted
- NSTP subjects get an automatic 50% discount before any additional discounts

### Q: What's the minimum down payment?
**A:** The minimum down payment is ₱500, with a default of ₱3,500.

### Q: Can I modify fees manually?
**A:** Yes, administrators can override:
- Individual subject fees
- Total lecture/laboratory amounts
- Discount percentages
- Miscellaneous fees
- Down payment amounts

## Technical Issues

### Q: The form isn't calculating fees automatically
**A:** Try these solutions:
1. Refresh the page and try again
2. Check if JavaScript is enabled in your browser
3. Clear your browser cache
4. Ensure you have a stable internet connection
5. Contact technical support if the issue persists

### Q: I'm getting validation errors when saving
**A:** Common validation issues:
- Missing required fields (student, semester, school year)
- Down payment below ₱500 minimum
- Invalid discount percentage (must be 0-100%)
- Duplicate subject enrollments

### Q: The page is loading slowly
**A:** Performance tips:
1. Close unnecessary browser tabs
2. Clear browser cache and cookies
3. Check your internet connection
4. Try using a different browser
5. Contact IT support for persistent issues

## Enrollment Process Questions

### Q: Can I modify an enrollment after it's saved?
**A:** Yes, you can edit enrollments through the Student Enrollments list. Click on the enrollment record and select "Edit."

### Q: What happens if I enroll a student in conflicting class schedules?
**A:** The system has conflict detection that will warn you about:
- Overlapping class times
- Double enrollment in the same subject
- Schedule conflicts with other enrolled classes

### Q: How do I handle late enrollments?
**A:**
1. Follow your institution's late enrollment policy
2. Check if subjects are still available
3. Verify any additional fees for late enrollment
4. Process the enrollment following standard procedures

### Q: Can students be enrolled in summer classes?
**A:** Yes, select "Summer" as the semester when creating the enrollment. Ensure summer subjects are available and properly configured.

## Payment and Financial Questions

### Q: How do I record additional payments?
**A:** Additional payments are typically handled through the payment processing system. The enrollment system tracks the initial down payment and calculates the remaining balance.

### Q: What if a student wants to change their down payment amount?
**A:** You can edit the enrollment record and modify the down payment amount. The system will automatically recalculate the remaining balance.

### Q: How are refunds handled?
**A:** Refund processing depends on your institution's refund policy. The enrollment system can track the financial details, but refund processing typically requires additional administrative steps.

## System Administration Questions

### Q: How do I set up course fee rates?
**A:** Course fee rates are configured in the Course management section. Each course should have:
- Lecture per unit rate
- Laboratory per unit rate
- Miscellaneous fees

### Q: How do I add new subjects?
**A:** Navigate to the Subjects section and create new subject records with:
- Unique subject code
- Subject name and description
- Unit assignments (lecture and laboratory)
- Course association

### Q: Can I import student data in bulk?
**A:** Bulk import capabilities depend on your system configuration. Contact your system administrator for information about data import procedures.

## Error Messages

### "Student not found"
**Solution:** Verify the student exists in the system and is properly activated.

### "Subject not available"
**Solution:** Check if the subject is offered for the current academic period and assigned to the student's course.

### "Insufficient permissions"
**Solution:** Contact your administrator to verify you have the necessary permissions for enrollment operations.

### "Validation failed"
**Solution:** Review all form fields for missing or invalid data, particularly required fields and numeric values.

### "Calculation error"
**Solution:** Verify course fee rates are configured and subject units are properly assigned.

## Best Practices

### For Efficient Enrollment
1. **Prepare Information**: Have student details and subject lists ready
2. **Check Prerequisites**: Verify students meet subject requirements
3. **Validate Schedules**: Ensure no time conflicts
4. **Review Calculations**: Double-check fee calculations before saving
5. **Save Regularly**: Save work frequently to prevent data loss

### For Accurate Records
1. **Verify Student Data**: Confirm student information is current
2. **Check Academic Periods**: Ensure correct semester and year
3. **Validate Subjects**: Confirm subject codes and units
4. **Review Fees**: Verify all calculations are correct
5. **Document Changes**: Keep records of any manual overrides

## Getting Additional Help

### When to Contact Support
- Persistent technical issues
- System errors or bugs
- Permission-related problems
- Data corruption or loss
- Integration issues

### Information to Provide
When contacting support, include:
- Your username and role
- Specific error messages
- Steps to reproduce the issue
- Browser and operating system information
- Screenshots if applicable

### Emergency Procedures
For critical issues during enrollment periods:
1. Document the issue immediately
2. Contact technical support
3. Use alternative enrollment methods if available
4. Communicate with affected students
5. Follow institutional emergency procedures

## Related Documentation

For more detailed information, see:
- [Enrollment Process Guide](enrollment.process)
- [Tuition Calculation Details](tuition.calculation)
- [Subject Management](subjects.overview)
- [System Administration](admin.overview)

