---
title: Installation Guide
icon: heroicon-o-cog-6-tooth
order: 1
group: Installation
---

# DCCP Admin V2 - Complete Installation Guide

Welcome to the ==comprehensive installation guide== for DCCP Admin V2, a powerful Laravel Filament-based enrollment management system.

> 🚀 **Quick Start:** Choose your preferred installation method below for a ==one-command setup== experience.

## Installation Methods

| Method | Difficulty | Time Required | Best For |
|--------|------------|---------------|----------|
| **Docker (Recommended)** | ⭐ Easy | 5 minutes | Production & Development |
| **One-Command Script** | ⭐ Easy | 10 minutes | Quick local setup |
| **Manual Installation** | ⭐⭐⭐ Advanced | 30 minutes | Custom configurations |

## System Requirements

### Minimum Requirements

| Component | Requirement | Notes |
|-----------|-------------|-------|
| **PHP** | 8.4+ | With required extensions |
| **Database** | PostgreSQL 13+ | Primary database |
| **Memory** | 512MB RAM | Minimum for basic operation |
| **Storage** | 2GB free space | For application and data |
| **Node.js** | 18+ | For asset compilation |

### Required PHP Extensions

> ⚠️ **Critical:** All these extensions are ==required== for proper functionality.

| Extension | Purpose | Auto-installed |
|-----------|---------|----------------|
| `ext-intl` | Internationalization | ✅ Docker |
| `ext-pgsql` | PostgreSQL support | ✅ Docker |
| `ext-redis` | Redis caching | ✅ Docker |
| `ext-sockets` | WebSocket support | ✅ Docker |
| `ext-sodium` | Encryption | ✅ Docker |
| `ext-zip` | File compression | ✅ Docker |

## Quick Installation Options

### 🐳 Option 1: Docker (Recommended)

**Single command installation with all dependencies:**

```bash
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.sh | bash
```

**What this does:**
- Downloads and runs the complete setup script
- Installs Docker and Docker Compose (if needed)
- Sets up the application with PostgreSQL and Redis
- Configures environment variables
- Runs database migrations
- Creates default admin user

### ⚡ Option 2: Manual Docker Setup

```bash
# Clone the repository
git clone https://github.com/yukazakiri/DccpAdminV2.git
cd DccpAdminV2

# Copy environment file
cp .env.example .env

# Generate application key
docker run --rm -v $(pwd):/app composer:latest composer install --no-dev --optimize-autoloader
docker run --rm -v $(pwd):/app php:8.4-cli php artisan key:generate

# Start services
docker-compose up -d

# Run migrations
docker-compose exec app php artisan migrate --seed
```

### 💻 Option 3: Local Development Setup

```bash
# Install dependencies
composer install
npm install

# Setup environment
cp .env.example .env
php artisan key:generate

# Build assets
npm run build

# Start development server
composer run dev
```

## Platform-Specific Installation

### Windows

#### Prerequisites Installation
```powershell
# Install Chocolatey (if not installed)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install required software
choco install docker-desktop git nodejs php composer postgresql -y

# Restart PowerShell and continue with Docker setup
```

#### One-Command Windows Setup
```powershell
# Download and run installation script
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.ps1" -OutFile "install.ps1"
PowerShell -ExecutionPolicy Bypass -File install.ps1
```

### macOS

#### Prerequisites Installation
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required software
brew install docker docker-compose git node php composer postgresql

# Start Docker Desktop
open /Applications/Docker.app
```

#### One-Command macOS Setup
```bash
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install-macos.sh | bash
```

### Linux (Ubuntu/Debian)

#### Prerequisites Installation
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install other dependencies
sudo apt install -y git nodejs npm php8.4 php8.4-cli composer postgresql-client

# Logout and login to apply Docker group changes
```

#### One-Command Linux Setup
```bash
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install-linux.sh | bash
```

### Linux (CentOS/RHEL/Fedora)

#### Prerequisites Installation
```bash
# Install Docker
sudo dnf install -y docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# Install other dependencies
sudo dnf install -y git nodejs npm php php-cli composer postgresql

# Logout and login to apply Docker group changes
```

#### One-Command CentOS/RHEL Setup
```bash
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install-centos.sh | bash
```

## Docker Deployment Options

### Development Environment
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
```

### Production Environment
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  app:
    image: ghcr.io/yukazakiri/dccpadminv2:latest
    ports:
      - "80:8000"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
```

## Environment Configuration

### Essential Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `APP_NAME` | Application name | DCCP Admin V2 |
| `APP_ENV` | Environment | production |
| `APP_KEY` | Encryption key | base64:... |
| `APP_URL` | Application URL | https://admin.dccp.edu.ph |
| `DB_HOST` | Database host | localhost |
| `DB_DATABASE` | Database name | dccpadminv2 |
| `DB_USERNAME` | Database user | dccp_user |
| `DB_PASSWORD` | Database password | secure_password |

### Complete Environment Setup

```bash
# Copy and edit environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure database settings
sed -i 's/DB_DATABASE=.*/DB_DATABASE=dccpadminv2/' .env
sed -i 's/DB_USERNAME=.*/DB_USERNAME=dccp_user/' .env
sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=your_secure_password/' .env
```

## Post-Installation Setup

### Database Migration and Seeding

```bash
# Run migrations
php artisan migrate

# Seed database with sample data
php artisan db:seed

# Create admin user
php artisan make:filament-user
```

### Asset Compilation

```bash
# Install Node.js dependencies
npm install

# Build production assets
npm run build

# For development with hot reload
npm run dev
```

### Permissions Setup

```bash
# Set proper permissions (Linux/macOS)
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache

# For development
chmod -R 775 storage bootstrap/cache
```

## Verification and Testing

### Health Check Commands

```bash
# Check application status
php artisan about

# Test database connection
php artisan migrate:status

# Verify queue workers
php artisan queue:work --once

# Test email configuration
php artisan mail:test
```

### Access the Application

| Service | URL | Credentials |
|---------|-----|-------------|
| **Admin Panel** | http://localhost:8000/admin | Created during setup |
| **API Documentation** | http://localhost:8000/docs | N/A |
| **Knowledge Base** | http://localhost:8000/kb | Same as admin |

## Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| **Permission denied** | Run `chmod -R 775 storage` |
| **Database connection failed** | Check DB credentials in `.env` |
| **Assets not loading** | Run `npm run build` |
| **Queue not processing** | Start with `php artisan queue:work` |

### Getting Help

> 💬 **Support:** Need help? Check our [Troubleshooting Guide](../troubleshooting/faq) or contact support.

## Next Steps

After successful installation:

1. **Configure System Settings** - Set up courses, subjects, and fee structures
2. **Create User Accounts** - Add staff and administrator accounts
3. **Import Student Data** - Bulk import existing student records
4. **Test Enrollment Process** - Perform test enrollments
5. **Setup Backups** - Configure automated backup procedures

## Advanced Installation Options

### 🏢 Enterprise Deployment

For large-scale deployments with high availability requirements:

```bash
# Multi-server setup with load balancing
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install-enterprise.sh | bash
```

### ☁️ Cloud Platform Deployment

| Platform | One-Click Deploy | Documentation |
|----------|------------------|---------------|
| **AWS** | [![Deploy to AWS](https://img.shields.io/badge/Deploy%20to-AWS-orange)](https://aws.amazon.com/marketplace) | [AWS Guide](../deployment/aws) |
| **Google Cloud** | [![Deploy to GCP](https://img.shields.io/badge/Deploy%20to-GCP-blue)](https://cloud.google.com/marketplace) | [GCP Guide](../deployment/gcp) |
| **Azure** | [![Deploy to Azure](https://img.shields.io/badge/Deploy%20to-Azure-blue)](https://azuremarketplace.microsoft.com) | [Azure Guide](../deployment/azure) |
| **DigitalOcean** | [![Deploy to DO](https://img.shields.io/badge/Deploy%20to-DigitalOcean-blue)](https://marketplace.digitalocean.com) | [DO Guide](../deployment/digitalocean) |

### 🐳 Container Orchestration

| Platform | Configuration | Use Case |
|----------|---------------|----------|
| **Docker Swarm** | `docker-compose.swarm.yml` | Multi-node deployment |
| **Kubernetes** | `k8s/` directory | Enterprise orchestration |
| **Portainer** | `portainer-stack.yml` | GUI management |

## Installation Scripts Reference

### Available Scripts

| Script | Platform | Purpose |
|--------|----------|---------|
| `install.sh` | Linux/macOS | Universal installer |
| `install-windows.ps1` | Windows | PowerShell installer |
| `install-linux.sh` | Linux | Linux-specific |
| `install-macos.sh` | macOS | macOS-specific |
| `install-centos.sh` | CentOS/RHEL | RedHat-based systems |
| `docker-deploy.sh` | All | Docker deployment |

### Script Parameters

```bash
# Custom installation directory
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.sh | bash -s -- --dir=/custom/path

# Development mode
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.sh | bash -s -- --dev

# Skip prerequisites
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/install.sh | bash -s -- --skip-prereq
```

## System Compatibility

### Operating System Support

| OS Family | Versions | Status | Notes |
|-----------|----------|--------|-------|
| **Ubuntu** | 20.04, 22.04, 24.04 | ✅ Fully Supported | Recommended |
| **Debian** | 11, 12 | ✅ Fully Supported | Stable |
| **CentOS** | 8, 9 | ✅ Fully Supported | Enterprise |
| **RHEL** | 8, 9 | ✅ Fully Supported | Enterprise |
| **Fedora** | 38, 39, 40 | ✅ Fully Supported | Latest features |
| **macOS** | 12+, 13+, 14+ | ✅ Fully Supported | Intel & Apple Silicon |
| **Windows** | 10, 11 | ✅ Fully Supported | WSL2 recommended |

### Hardware Requirements

#### Minimum Requirements

| Component | Specification | Notes |
|-----------|---------------|-------|
| **CPU** | 1 core, 2.0 GHz | Basic operation |
| **RAM** | 2GB | Minimum for Docker |
| **Storage** | 5GB free space | Application + data |
| **Network** | Broadband internet | For downloads |

#### Recommended Requirements

| Component | Specification | Benefits |
|-----------|---------------|---------|
| **CPU** | 2+ cores, 2.5+ GHz | Better performance |
| **RAM** | 4GB+ | Smooth operation |
| **Storage** | 20GB+ SSD | Fast I/O operations |
| **Network** | Stable connection | Reliable updates |

#### Production Requirements

| Component | Specification | Purpose |
|-----------|---------------|---------|
| **CPU** | 4+ cores, 3.0+ GHz | Handle concurrent users |
| **RAM** | 8GB+ | Large datasets |
| **Storage** | 50GB+ SSD | Database growth |
| **Network** | Dedicated bandwidth | User experience |

### Browser Compatibility

| Browser | Minimum Version | Recommended | Notes |
|---------|----------------|-------------|-------|
| **Chrome** | 90+ | Latest | Best performance |
| **Firefox** | 88+ | Latest | Full compatibility |
| **Safari** | 14+ | Latest | macOS/iOS |
| **Edge** | 90+ | Latest | Windows default |

## Security Considerations

### Network Security

| Component | Configuration | Purpose |
|-----------|---------------|---------|
| **Firewall** | Ports 80, 443 only | Restrict access |
| **SSL/TLS** | Certificate required | Encrypt traffic |
| **VPN** | Optional | Secure admin access |

### Application Security

```bash
# Security hardening script
curl -fsSL https://raw.githubusercontent.com/yukazakiri/DccpAdminV2/main/security-hardening.sh | bash
```

## Performance Optimization

### Database Optimization

```sql
-- PostgreSQL performance tuning
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

### Application Optimization

```bash
# PHP optimization
docker-compose exec app php artisan optimize
docker-compose exec app php artisan config:cache
docker-compose exec app php artisan route:cache
docker-compose exec app php artisan view:cache
```

---

*Continue to [Docker Guide](docker-guide) for containerized deployment or [System Configuration](../configuration/overview) for detailed setup instructions.*
