---
title: Tuition Calculation System
icon: heroicon-o-calculator
order: 1
group: Tuition
---

# Tuition Calculation System

The DCCP Admin system features a sophisticated tuition calculation engine that automatically computes student fees based on enrolled subjects, course rates, and applicable discounts.

## How Tuition is Calculated

### Step 1: Base Fee Calculation

For each enrolled subject, the system calculates fees using this formula:

```
Lecture Fee = Total Units × Course Lecture Rate
Laboratory Fee = Laboratory Units × Course Laboratory Rate
```

**Important Notes:**
- ==Lecture fees are calculated using **total units** (lecture + laboratory)==
- ==Laboratory fees are calculated using **laboratory units only**==
- Each course has its own per-unit rates

### Step 2: Special Case Handling

#### NSTP Subjects (50% Discount)
```
If subject code contains "NSTP":
    Lecture Fee = (Total Units × Course Rate) × 0.5
    Laboratory Fee = Normal calculation
```

#### Modular Subjects (Fixed Fee)
```
If subject is marked as modular:
    Lecture Fee = ₱2,400 (fixed)
    Laboratory Fee = ₱0 (no lab fees)
```

### Step 3: Discount Application

Discounts are applied **only to the lecture portion** of tuition:

```
Discounted Lecture = Total Lecture Fees × (1 - Discount Percentage / 100)
Final Tuition = Discounted Lecture + Total Laboratory Fees
```

> ⚠️ **Important**: Laboratory fees and miscellaneous fees are **never discounted**

### Step 4: Final Calculation

```
Overall Total = Final Tuition + Miscellaneous Fees
Balance = Overall Total - Down Payment
```

## Detailed Example

Let's walk through a complete calculation:

### Student Information
- **Course**: BSIT
- **Lecture Rate**: ₱500 per unit
- **Laboratory Rate**: ₱800 per unit
- **Miscellaneous Fee**: ₱3,500

### Enrolled Subjects

| Subject | Lecture Units | Lab Units | Total Units | Type |
|---------|---------------|-----------|-------------|------|
| Programming 1 | 3 | 1 | 4 | Regular |
| Mathematics | 3 | 0 | 3 | Regular |
| NSTP 1 | 1 | 0 | 1 | NSTP |

### Calculation Process

#### 1. Individual Subject Fees

**Programming 1:**
```
Lecture Fee = 4 units × ₱500 = ₱2,000
Lab Fee = 1 unit × ₱800 = ₱800
Subject Total = ₱2,800
```

**Mathematics:**
```
Lecture Fee = 3 units × ₱500 = ₱1,500
Lab Fee = 0 units × ₱800 = ₱0
Subject Total = ₱1,500
```

**NSTP 1 (50% discount):**
```
Lecture Fee = 1 unit × ₱500 × 0.5 = ₱250
Lab Fee = 0 units × ₱800 = ₱0
Subject Total = ₱250
```

#### 2. Totals Before Discount
```
Total Lecture Fees = ₱2,000 + ₱1,500 + ₱250 = ₱3,750
Total Laboratory Fees = ₱800 + ₱0 + ₱0 = ₱800
```

#### 3. Apply 10% Discount
```
Discounted Lecture = ₱3,750 × (1 - 10/100) = ₱3,375
Final Tuition = ₱3,375 + ₱800 = ₱4,175
```

#### 4. Add Miscellaneous and Calculate Balance
```
Overall Total = ₱4,175 + ₱3,500 = ₱7,675
Down Payment = ₱3,500
Balance = ₱7,675 - ₱3,500 = ₱4,175
```

## Fee Structure Details

### Course-Based Rates

Each course defines its own fee structure:

| Component | Description | Example |
|-----------|-------------|---------|
| `lec_per_unit` | Cost per lecture unit | ₱500 |
| `lab_per_unit` | Cost per laboratory unit | ₱800 |
| `miscelaneous` | Fixed miscellaneous fees | ₱3,500 |

### Default Values

When course rates are not set:
- Lecture rate: ₱0 per unit
- Laboratory rate: ₱0 per unit
- Miscellaneous: ₱3,500 (system default)

## Discount System

### Available Discounts
- Range: 0% to 100%
- Increments: 5% (0%, 5%, 10%, 15%, ..., 100%)
- Application: Lecture fees only

### Discount Rules
1. **Lecture Fees**: Fully discountable
2. **Laboratory Fees**: Never discounted
3. **Miscellaneous Fees**: Never discounted
4. **NSTP Subjects**: Discount applies to already-reduced lecture fees

## Manual Override System

Administrators can manually override calculated amounts:

### Override Options
- Individual subject lecture fees
- Individual subject laboratory fees
- Total lecture amount
- Total laboratory amount
- Discount percentage
- Miscellaneous fees
- Down payment amount

### Override Behavior
- Manual changes set `is_manually_modified` flag
- System preserves manual overrides during recalculations
- Discounts still apply to manually set amounts
- Balance automatically recalculates

## Real-Time Calculation

### Form Interactions

The system provides instant updates when:

1. **Adding/Removing Subjects**
   - Recalculates all totals
   - Updates fee breakdown
   - Adjusts balance

2. **Changing Modular Status**
   - Switches between calculated and fixed fees
   - Updates subject totals
   - Recalculates overall total

3. **Adjusting Discount**
   - Applies new discount to lecture fees
   - Preserves laboratory fees
   - Updates final amounts

4. **Modifying Down Payment**
   - Recalculates remaining balance
   - Validates minimum payment
   - Updates payment status

### Validation Rules

- **Down Payment**: Minimum ₱500
- **Discount**: 0-100% range
- **Subject Fees**: Must be numeric
- **Required Fields**: Student, subjects, academic info

## Database Storage

### StudentTuition Table

The calculated amounts are stored in these fields:

| Field | Description |
|-------|-------------|
| `total_tuition` | Final tuition after discounts |
| `total_lectures` | Lecture fees after discount |
| `total_laboratory` | Laboratory fees (no discount) |
| `total_miscelaneous_fees` | Miscellaneous fees |
| `discount` | Discount percentage applied |
| `downpayment` | Initial payment amount |
| `overall_tuition` | Total including miscellaneous |
| `total_balance` | Remaining balance |

## Troubleshooting

### Common Issues

**Incorrect Calculations**
- Check course fee rates are set correctly
- Verify subject unit counts
- Confirm discount is applied only to lectures

**Missing Fees**
- Ensure course has defined rates
- Check if subject has proper unit assignments
- Verify miscellaneous fee is set

**Override Problems**
- Clear manual modifications if needed
- Recalculate totals using service
- Check for validation errors

## Next Steps

- [Student Enrollment Process](enrollment.process) - How to enroll students
- [Subject Management](subjects.overview) - Managing subjects and units
- [Payment Processing](payments.overview) - Handling student payments
- [Troubleshooting](troubleshooting.tuition) - Common tuition issues

