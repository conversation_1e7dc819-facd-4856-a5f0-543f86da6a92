---
title: Enrollment System Overview
icon: heroicon-o-academic-cap
order: 1
group: Enrollment
---

# Enrollment System Overview

The DCCP Admin enrollment system is designed to handle the complete student enrollment lifecycle, from initial application to class assignment and tuition calculation.

## Key Features

### 🎓 Comprehensive Student Management
- Student registration and profile management
- Academic history tracking
- Course and program assignment
- Multiple student types (College, SHS)

### 💰 Automated Tuition Calculation
- Real-time fee calculation based on enrolled subjects
- Automatic discount application
- Special pricing for NSTP and modular courses
- Payment tracking and balance management

### 📚 Subject and Class Management
- Subject enrollment with lecture and laboratory components
- Class scheduling and capacity management
- Prerequisite checking
- Conflict detection

### 📊 Real-time Updates
- Live form calculations
- Instant fee updates when subjects change
- Automatic balance recalculation
- Dynamic form validation

## Enrollment Workflow

### 1. Student Selection
```
Select Student → Verify Information → Set Academic Period
```

The enrollment process begins by selecting an existing student or creating a new student record. The system automatically populates student information including:

- Full name and contact details
- Current course and academic year
- Email address
- Academic standing

### 2. Academic Period Setup
```
Set Semester → Set School Year → Configure Settings
```

Each enrollment is tied to a specific academic period:

- **Semester**: 1st Semester, 2nd Semester, Summer
- **School Year**: Academic year (e.g., 2023-2024)
- **Academic Level**: Based on student's current year

### 3. Subject Selection
```
Browse Subjects → Select Courses → Configure Options
```

Students can enroll in multiple subjects with various options:

- **Regular Subjects**: Standard lecture and laboratory courses
- **NSTP Subjects**: Special pricing (50% discount on lecture fees)
- **Modular Subjects**: Fixed fee structure (₱2,400)
- **Laboratory Components**: Additional lab fees when applicable

### 4. Fee Calculation
```
Calculate Base Fees → Apply Discounts → Add Miscellaneous → Set Payment
```

The system automatically calculates:

- **Base Tuition**: Subject units × course rates
- **Discounts**: Applied only to lecture portions
- **Miscellaneous Fees**: Course-specific or default fees
- **Payment Plan**: Down payment and remaining balance

## Student Types

### College Students
- Full degree programs
- Standard tuition calculation
- Complete academic tracking
- Prerequisite enforcement

### Senior High School (SHS) Students
- Specialized curriculum
- Modified fee structure
- Track-based subject selection
- Strand-specific requirements

## Fee Structure Components

### Base Rates
- **Lecture Fee**: Per unit rate × lecture units
- **Laboratory Fee**: Per unit rate × laboratory units
- **Miscellaneous**: Fixed course fees

### Special Pricing
- **NSTP Discount**: 50% off lecture fees for NSTP subjects
- **Modular Courses**: Fixed ₱2,400 fee regardless of units
- **Laboratory Only**: No lecture fees for lab-only subjects

### Discount System
- Percentage-based discounts (0% to 100% in 5% increments)
- Applied only to lecture portion of tuition
- Does not affect laboratory or miscellaneous fees

## Payment Management

### Down Payment
- Minimum payment: ₱500
- Default amount: ₱3,500
- Customizable per enrollment

### Balance Calculation
- Automatic calculation: Total fees - Down payment
- Real-time updates when fees change
- Payment status tracking

## Data Validation

### Required Information
- Student selection
- Academic period (semester and school year)
- At least one subject enrollment
- Valid down payment amount

### Automatic Checks
- Subject availability verification
- Prerequisite validation
- Schedule conflict detection
- Capacity limit enforcement

## Integration Points

### Student Records
- Links to existing student profiles
- Updates academic history
- Tracks enrollment status

### Academic Management
- Connects to class schedules
- Updates subject enrollments
- Manages academic records

### Financial System
- Generates tuition records
- Tracks payment history
- Manages account balances

## Next Steps

Ready to start enrolling students? Check out these guides:

- [Student Enrollment Process](enrollment.process) - Step-by-step enrollment guide
- [Tuition Calculation Details](tuition.calculation) - Understanding fee calculations
- [Subject Management](subjects.overview) - Managing courses and subjects
- [Troubleshooting](troubleshooting.enrollment) - Common enrollment issues

