---
title: Student Enrollment Process
icon: heroicon-o-user-plus
order: 2
group: Enrollment
---

# Student Enrollment Process

This guide walks you through the complete process of enrolling a student in the DCCP Admin system, from initial setup to final confirmation.

## Before You Start

### Prerequisites
- Student record must exist in the system
- Current semester and school year must be set
- Course fee rates must be configured
- Subjects must be available for the academic period

### Required Permissions
- Access to Student Enrollment Resource
- Create enrollment permissions
- View student records

## Step-by-Step Enrollment

### Step 1: Access the Enrollment Form

1. Navigate to **Student Enrollments** in the admin panel
2. Click **New Enrollment** button
3. The enrollment form will open

### Step 2: Select Student

#### Using the Student Search
1. Click on the **Student** field
2. Type the student's name or ID number
3. Select the correct student from the dropdown

#### Student Information Auto-Population
Once selected, the system automatically fills:
- **Full Name**: Student's complete name
- **Email**: Student's email address (if available)
- **Course**: Student's current course/program
- **Academic Year**: Student's current academic year

> 💡 **Tip**: If student information appears incorrect, update the student record first before proceeding with enrollment.

### Step 3: Set Academic Period

#### Semester Selection
Choose the appropriate semester:
- **1st Semester**
- **2nd Semester**
- **Summer**

#### School Year
- Select the current academic year (e.g., "2023-2024")
- This should match the current academic period

> ⚠️ **Important**: The academic period determines which subjects are available for enrollment.

### Step 4: Configure Enrollment Settings

#### Down Payment
- **Default**: ₱3,500
- **Minimum**: ₱500
- **Custom Amount**: Enter any amount ≥ ₱500

#### Discount (Optional)
- Select from 0% to 100% in 5% increments
- Applied only to lecture fees
- Does not affect laboratory or miscellaneous fees

### Step 5: Add Subjects

#### Subject Selection Process
1. Click **Add Subject** button
2. Choose subject from the dropdown
3. Configure subject options:
   - **Modular**: Toggle if this is a modular course
   - **Lecture Fee**: Auto-calculated (can be manually overridden)
   - **Laboratory Fee**: Auto-calculated (can be manually overridden)

#### Subject Types and Calculations

**Regular Subjects**
```
Lecture Fee = Total Units × Course Lecture Rate
Laboratory Fee = Lab Units × Course Laboratory Rate
```

**NSTP Subjects** (Automatic 50% discount)
```
Lecture Fee = (Total Units × Course Rate) × 0.5
Laboratory Fee = Normal calculation
```

**Modular Subjects**
```
Lecture Fee = ₱2,400 (fixed)
Laboratory Fee = ₱0 (no lab fees)
```

#### Adding Multiple Subjects
- Repeat the process for each subject
- The system will automatically update totals
- Real-time calculation shows running totals

### Step 6: Review Calculations

#### Fee Breakdown Display
The form shows real-time calculations:

- **Total Lecture Fees**: Sum of all lecture fees
- **Total Laboratory Fees**: Sum of all laboratory fees
- **Discount Applied**: Amount deducted from lecture fees
- **Subtotal**: Lecture + Laboratory after discount
- **Miscellaneous Fees**: Course-specific fees
- **Overall Total**: Final amount due
- **Balance**: Amount remaining after down payment

#### Verification Checklist
✅ Student information is correct
✅ Academic period is accurate
✅ All required subjects are added
✅ Fee calculations look reasonable
✅ Down payment amount is appropriate

### Step 7: Save Enrollment

1. Review all information one final time
2. Click **Create** button
3. System will validate all data
4. Enrollment record will be created

## Form Fields Reference

### Student Information Section

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| Student | Select | Yes | Choose existing student |
| Full Name | Text | Auto | Auto-populated from student |
| Email | Email | Auto | Auto-populated from student |
| Course | Select | Auto | Auto-populated from student |
| Academic Year | Text | Auto | Auto-populated from student |

### Academic Period Section

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| Semester | Select | Yes | 1st, 2nd, or Summer |
| School Year | Select | Yes | Academic year |

### Financial Section

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| Down Payment | Number | Yes | Minimum ₱500 |
| Discount | Select | No | 0% to 100% in 5% increments |

### Subject Enrollment Section

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| Subject | Select | Yes | Available subjects |
| Modular | Toggle | No | Fixed ₱2,400 fee |
| Lecture Fee | Number | Auto | Calculated or manual |
| Laboratory Fee | Number | Auto | Calculated or manual |

## Real-Time Features

### Automatic Calculations
- **Fee Updates**: Instant recalculation when subjects change
- **Discount Application**: Real-time discount application
- **Balance Updates**: Automatic balance calculation
- **Validation**: Live form validation

### Dynamic Form Behavior
- **Subject Availability**: Only shows available subjects
- **Fee Display**: Updates as you type
- **Error Messages**: Immediate validation feedback
- **Save State**: Preserves data during form interactions

## Validation Rules

### Required Fields
- Student selection
- Semester and school year
- At least one subject
- Down payment amount

### Business Rules
- Down payment ≥ ₱500
- Discount between 0% and 100%
- Subject fees must be numeric
- Cannot enroll in same subject twice

### System Checks
- Student must exist
- Subject must be available
- Academic period must be valid
- Course rates must be configured

## Common Scenarios

### Scenario 1: Regular College Student
```
Student: John Doe (BSIT)
Subjects: 5 regular subjects
Discount: 10%
Down Payment: ₱3,500
```

### Scenario 2: SHS Student with NSTP
```
Student: Jane Smith (STEM)
Subjects: 4 regular + 1 NSTP
Discount: 0%
Down Payment: ₱2,000
```

### Scenario 3: Modular Course Enrollment
```
Student: Bob Wilson (BSBA)
Subjects: 2 regular + 1 modular
Discount: 5%
Down Payment: ₱4,000
```

## After Enrollment

### What Happens Next
1. **Tuition Record Created**: StudentTuition record generated
2. **Subject Enrollments**: Individual subject records created
3. **Academic History Updated**: Student's enrollment history updated
4. **Payment Tracking**: Balance and payment status tracked

### Available Actions
- **View Enrollment**: Review enrollment details
- **Edit Enrollment**: Modify enrollment if needed
- **Print Records**: Generate enrollment documents
- **Process Payments**: Record additional payments

## Troubleshooting

### Common Issues

**Student Not Found**
- Verify student exists in system
- Check spelling of name/ID
- Ensure student record is active

**Subjects Not Available**
- Check academic period settings
- Verify subject is offered this semester
- Confirm course prerequisites

**Calculation Errors**
- Verify course fee rates are set
- Check subject unit assignments
- Confirm discount application

**Save Errors**
- Review validation messages
- Check required fields
- Verify data formats

## Next Steps

- [Understanding Tuition Calculations](tuition.calculation) - How fees are calculated
- [Managing Student Records](students.overview) - Student management
- [Subject Administration](subjects.overview) - Managing subjects and courses
- [Payment Processing](payments.overview) - Handling payments

