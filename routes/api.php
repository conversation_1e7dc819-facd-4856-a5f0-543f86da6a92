<?php

use App\Http\Controllers\Api\GeneralSettingsApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public API routes
Route::prefix('v1')->middleware('api')->group(function (): void {
    Route::get('/settings', [GeneralSettingsApiController::class, 'index']);
});
